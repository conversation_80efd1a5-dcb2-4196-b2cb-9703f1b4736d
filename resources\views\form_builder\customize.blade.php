@extends('layouts.admin')
@section('page-title')
    {{ __('Form Customization') }} - {{ $form->name }}
@endsection

@push('css-page')
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('form_builder.index')}}">{{__('Form Builder')}}</a></li>
    <li class="breadcrumb-item">{{__('Form Customization')}}</li>
@endsection

@push('css-page')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/themes/ui-lightness/jquery-ui.min.css">
@endpush



@push('script-page')
<style>
    .style-controls {
        background: white;
        border-radius: 6px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        margin-bottom: 15px;
    }

    .color-picker-wrapper {
        position: relative;
        display: inline-block;
    }

    .color-preview {
        width: 30px;
        height: 30px;
        border-radius: 4px;
        border: 1px solid #dee2e6;
        cursor: pointer;
        display: inline-block;
        margin-left: 8px;
    }

    .form-preview {
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        height: 650px;
        overflow-y: auto;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
    }

    .form-preview-container {
        width: 100%;
        max-width: 100%;
        margin: 0 auto;
        padding-top: 16px;
    }

    /* Sticky Live Preview Section */
    .col-md-7 {
        position: sticky;
        top: 20px;
        height: fit-content;
        max-height: calc(100vh - 40px);
        overflow-y: auto;
        z-index: 10;
    }

    /* Ensure the preview container scrolls properly */
    .form-preview {
        max-height: calc(100vh - 120px);
        overflow-y: auto;
    }

    /* Custom scrollbar for preview */
    .form-preview::-webkit-scrollbar {
        width: 6px;
    }

    .form-preview::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .form-preview::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .form-preview::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Simple Form Preview Styling */
    #form-preview {
        background: #ffffff;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        width: 90%;
        max-width: 600px;
        margin: 0 auto;
    }

    .preview-title {
        font-weight: 600;
        color: #495057;
        font-size: 18px;
        margin-bottom: 16px;
        text-align: center;
        padding-bottom: 12px;
    }

    /* Country Code Dropdown Styling for Preview */
    .country-code-select {
        border-right: none !important;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        background-color: #f8f9fa !important;
        font-size: 14px !important;
        padding: 0.375rem 0.5rem !important;
    }

    .country-code-select + .form-control {
        border-left: none !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
    }

    .input-group .country-code-select:focus {
        box-shadow: none !important;
        border-color: #80bdff !important;
    }

    .input-group .form-control:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
    }

    /* Phone Input Container Styling for Preview */
    .phone-input-container {
        position: relative;
        display: flex;
        align-items: stretch;
        width: 100%;
    }

    .country-selector {
        position: relative;
        z-index: 10;
    }

    .country-dropdown-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 16px;
        background-color: #ffffff;
        border: 2px solid #e1e5e9;
        border-right: none;
        border-radius: 12px 0 0 12px;
        color: #2d3748;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.5;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        min-width: 90px;
        justify-content: center;
        height: 48px;
        box-sizing: border-box;
        outline: none;
    }

    .country-dropdown-btn:hover {
        background-color: #f7fafc;
        border-color: #cbd5e0;
    }

    .country-dropdown-btn:focus {
        outline: 0;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        border-color: #4f46e5;
    }

    .country-dropdown-btn .flag {
        font-size: 16px;
    }

    .country-dropdown-btn .code {
        font-weight: 500;
        font-size: 13px;
    }

    .country-dropdown-btn .ti-chevron-down {
        font-size: 12px;
        opacity: 0.7;
    }

    .phone-number-input {
        border-left: none !important;
        border-radius: 0 12px 12px 0 !important;
        flex: 1;
        height: 48px;
        box-sizing: border-box;
        padding: 12px 16px !important;
        font-size: 14px !important;
        border: 2px solid #e1e5e9 !important;
        background-color: #ffffff !important;
        transition: all 0.2s ease-in-out !important;
        color: #2d3748 !important;
    }

    .phone-number-input:focus {
        border-color: #4f46e5 !important;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
        outline: none !important;
    }

    .phone-number-input:hover {
        border-color: #cbd5e0 !important;
    }

    .phone-number-input::placeholder {
        color: #a0aec0 !important;
        font-weight: 400 !important;
    }

    .country-dropdown {
        max-height: 240px;
        overflow-y: auto;
        border: 2px solid #e1e5e9;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        background-color: #ffffff;
        padding: 8px 0;
    }

    .country-dropdown .dropdown-item {
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 400;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        color: #2d3748;
        border: none;
        background: none;
    }

    .country-dropdown .dropdown-item:hover {
        background-color: #f7fafc;
        color: #1a202c;
    }

    .country-dropdown .dropdown-item:active,
    .country-dropdown .dropdown-item.active {
        background-color: #4f46e5;
        color: white;
    }

    .country-dropdown {
        outline: none;
        tabindex: -1;
    }

    .country-dropdown .dropdown-item {
        outline: none;
    }

    /* Modern Input Field Styling for Preview */
    .preview-input.form-control {
        height: 48px !important;
        box-sizing: border-box !important;
        padding: 12px 16px !important;
        font-size: 14px !important;
        font-weight: 400 !important;
        line-height: 1.5 !important;
        border: 2px solid #e1e5e9 !important;
        border-radius: 12px !important;
        background-color: #ffffff !important;
        transition: all 0.2s ease-in-out !important;
        color: #2d3748 !important;
    }

    .preview-input.form-control:focus {
        border-color: #4f46e5 !important;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
        outline: none !important;
        background-color: #ffffff !important;
    }

    .preview-input.form-control:hover {
        border-color: #cbd5e0 !important;
    }

    .preview-input.form-control::placeholder {
        color: #a0aec0 !important;
        font-weight: 400 !important;
    }

    /* Modern Textarea Styling */
    textarea.preview-input.form-control {
        height: auto !important;
        min-height: 120px !important;
        resize: vertical !important;
        font-family: inherit !important;
    }

    /* Modern Select Dropdown Styling */
    select.preview-input.form-control {
        height: 48px !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
        background-position: right 12px center !important;
        background-repeat: no-repeat !important;
        background-size: 16px 12px !important;
        padding-right: 40px !important;
        appearance: none !important;
        -webkit-appearance: none !important;
        -moz-appearance: none !important;
    }

    /* Modern Checkbox and Radio Styling */
    .preview-input[type="checkbox"],
    .preview-input[type="radio"] {
        width: 18px !important;
        height: 18px !important;
        border: 2px solid #e1e5e9 !important;
        border-radius: 4px !important;
        background-color: #ffffff !important;
        cursor: pointer !important;
        transition: all 0.2s ease-in-out !important;
    }

    .preview-input[type="radio"] {
        border-radius: 50% !important;
    }

    .preview-input[type="checkbox"]:checked,
    .preview-input[type="radio"]:checked {
        background-color: #4f46e5 !important;
        border-color: #4f46e5 !important;
    }

    /* Modern Button Styling for Preview */
    .preview-submit-btn,
    .btn-primary {
        height: 48px !important;
        padding: 12px 24px !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        line-height: 1.5 !important;
        border: none !important;
        border-radius: 12px !important;
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
        color: #ffffff !important;
        cursor: pointer !important;
        transition: all 0.2s ease-in-out !important;
        text-transform: none !important;
        letter-spacing: 0.025em !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    .preview-submit-btn:hover,
    .btn-primary:hover {
        background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%) !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    }

    .preview-submit-btn:active,
    .btn-primary:active {
        transform: translateY(0) !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

    .preview-submit-btn:focus,
    .btn-primary:focus {
        outline: none !important;
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3) !important;
    }

    /* Modern Label Styling */
    .preview-label,
    .form-label {
        font-size: 14px !important;
        font-weight: 600 !important;
        color: #374151 !important;
        margin-bottom: 8px !important;
        display: block !important;
        line-height: 1.5 !important;
    }

    /* Form Group Spacing */
    .form-group {
        margin-bottom: 24px !important;
    }

    /* Required Field Asterisk */
    .text-danger {
        color: #ef4444 !important;
        font-weight: 600 !important;
        margin-left: 4px !important;
    }

    /* Compact Field Styling for More Fields */
    .preview-field-container {
        margin-bottom: 6px !important;
        padding: 4px 8px;
        background: transparent;
        border: none;
        border-radius: 0;
        transition: none;
    }

    .preview-field-container:hover {
        background: #f8f9fa;
    }

    .preview-label {
        font-weight: 500;
        color: #495057;
        font-size: 13px;
        margin-bottom: 2px !important;
    }

    .preview-label .text-danger {
        color: #dc3545 !important;
        margin-left: 2px;
    }

    /* Simple Input Styling */
    .preview-input {
        border: 1px solid #ced4da;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 13px;
        background: #ffffff;
        height: 28px;
        width: 100%;
    }

    .preview-input:focus {
        border-color: #6c757d !important;
        box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25) !important;
        outline: none !important;
    }

    /* Compact Field Actions */
    .field-actions {
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .preview-field-container:hover .field-actions {
        opacity: 1;
    }

    .field-actions .btn {
        padding: 2px 4px !important;
        font-size: 10px !important;
        border-radius: 3px !important;
        margin-left: 2px;
    }

    /* Simple Drag Handle */
    .drag-handle {
        color: #6c757d !important;
        cursor: grab;
        padding: 2px;
    }

    .drag-handle:hover {
        color: #495057 !important;
    }

    .drag-handle:active {
        cursor: grabbing;
    }

    /* Compact Form Controls */
    .form-check {
        margin-bottom: 3px !important;
        padding-left: 20px !important;
        display: flex !important;
        align-items: center !important;
        min-height: 24px !important;
    }

    .form-check-input {
        margin-top: 0 !important;
        margin-right: 6px !important;
        width: 16px !important;
        height: 16px !important;
        flex-shrink: 0 !important;
    }

    .form-check-label {
        font-size: 13px !important;
        color: #495057;
        margin-bottom: 0 !important;
        line-height: 1.2 !important;
        cursor: pointer !important;
    }

    /* Radio Button Specific Styling */
    .form-check-input[type="radio"] {
        border-radius: 50% !important;
    }

    /* Checkbox Specific Styling */
    .form-check-input[type="checkbox"] {
        border-radius: 3px !important;
    }

    /* Simple Submit Button - Allow dynamic styles to override */
    .preview-button {
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
        cursor: pointer;
    }

    .preview-button-container {
        text-align: center;
        padding-top: 12px;
        border-top: 1px solid #dee2e6;
        margin-top: 12px;
    }

    /* Compact Sortable Container */
    .sortable-container {
        min-height: 80px;
        padding: 2px;
    }

    /* Compact Layout */
    .field-input-container {
        gap: 4px;
    }

    .field-header {
        margin-bottom: 2px !important;
    }

    /* Select and Textarea Styling */
    .preview-input[type="select"],
    .preview-input.form-select,
    select.preview-input {
        min-height: 28px !important;
        height: 28px !important;
        padding: 4px 8px !important;
        font-size: 13px !important;
    }

    textarea.preview-input {
        min-height: 60px !important;
        height: 60px !important;
        resize: vertical;
        padding: 4px 8px !important;
        font-size: 13px !important;
        line-height: 1.3 !important;
    }

    /* Radio and Checkbox Groups */
    .form-check-input:checked {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
    }

    .form-check-input:focus {
        border-color: #6c757d !important;
        box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25) !important;
    }

    /* File Input Styling */
    .form-control[type="file"],
    input[type="file"].preview-input {
        padding: 4px 8px !important;
        font-size: 13px !important;
        height: 28px !important;
    }

    /* Date Input Styling */
    .form-control[type="date"],
    input[type="date"].preview-input {
        padding: 4px 8px !important;
        font-size: 13px !important;
        height: 28px !important;
    }

    /* Multi-select Styling */
    select[multiple].preview-input {
        min-height: 70px !important;
        height: 70px !important;
        padding: 4px 8px !important;
        font-size: 13px !important;
    }

    /* Empty State Styling */
    .sortable-container:empty::before {
        content: "Add fields to see preview";
        display: block;
        text-align: center;
        color: #6c757d;
        font-style: italic;
        padding: 20px;
        border: 1px dashed #ced4da;
        border-radius: 4px;
        background: #f8f9fa;
    }

    /* Responsive behavior for sticky preview */
    @media (max-width: 768px) {
        .col-md-7 {
            position: static;
            height: auto;
            max-height: none;
            overflow-y: visible;
        }

        .form-preview {
            max-height: none;
            overflow-y: visible;
        }

        .preview-title {
            font-size: 20px;
            margin-bottom: 20px;
        }

        .preview-field-container {
            margin-bottom: 12px !important;
            padding: 10px;
        }
    }

    #form-preview.card {
        border: none;
        border-radius: 16px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        margin-bottom: 24px;
        background: #ffffff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    #form-preview .card-body {
        padding: 12px;
        background: #ffffff;
        width: 100%;
        box-sizing: border-box;
    }

    .preview-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1a73e8;
        margin-bottom: 2rem;
        text-align: center;
        letter-spacing: -0.5px;
        position: relative;
    }



    .preview-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: #495057;
    }

    .preview-input {
        margin-bottom: 1rem;
        padding: 0.75rem;
        border: 1px solid #ced4da;
        border-radius: 0.375rem;
        font-size: 1rem;
    }

    .preview-button {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
        font-weight: 500;
        border-radius: 0.375rem;
    }

    /* Beautiful Drag and Drop Styles */
    .preview-field-container {
        position: relative;
        background: #ffffff;
        border: 2px solid #f1f3f4;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: move;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .preview-field-container:hover {
        background: #f8f9fa;
    }

    .preview-field-container.ui-sortable-helper {
        border-color: #28a745;
        background: #e8f5e8;
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.25);
        transform: rotate(1deg) scale(1.01);
        z-index: 1000;
    }

    .field-header {
        min-height: 32px;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f1f3f4;
    }

    .field-input-container {
        position: relative;
        background: #fafbfc;
        border-radius: 8px;
        padding: 8px;
        transition: all 0.3s ease;
    }

    .preview-field-container:hover .field-input-container {
        background: #f8f9ff;
        border: 1px solid #e8eaff;
    }

    .drag-handle {
        cursor: grab;
        color: #9aa0a6;
        padding: 8px;
        opacity: 0.6;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
        background: #ffffff;
        border: 1px solid #e8eaed;
        border-radius: 6px;
        margin-right: 12px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .preview-field-container:hover .drag-handle {
        opacity: 1;
        color: #4285f4;
        background: #f8f9ff;
        border-color: #d2e3fc;
        box-shadow: 0 2px 4px rgba(66, 133, 244, 0.1);
        transform: scale(1.05);
    }

    .drag-handle:active {
        cursor: grabbing;
        transform: scale(0.95);
    }

    .field-input {
        position: relative;
    }

    .field-actions {
        display: flex;
        gap: 6px;
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(10px);
    }

    .preview-field-container:hover .field-actions {
        opacity: 1;
        transform: translateX(0);
    }

    .field-actions .btn {
        padding: 6px 10px;
        font-size: 12px;
        line-height: 1;
        border-radius: 6px;
        border: none;
        font-weight: 500;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .field-actions .btn-outline-primary {
        background: #6c757d;
        color: white;
        border-color: #6c757d;
    }

    .field-actions .btn-outline-primary:hover {
        background: #5a6268;
        border-color: #5a6268;
    }

    .field-actions .btn-outline-danger {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .field-actions .btn-outline-danger:hover {
        background: #c82333;
        border-color: #c82333;
    }

    .field-actions .btn i {
        font-size: 13px;
    }

    .sortable-container {
        min-height: 120px;
        padding: 8px;
    }

    .ui-sortable-placeholder {
        border: 2px dashed #28a745;
        background: #e8f5e8;
        height: 80px;
        margin-bottom: 16px;
        border-radius: 6px;
        position: relative;
        animation: pulse 1.5s ease-in-out infinite;
    }

    .ui-sortable-placeholder::before {
        content: "Drop field here";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #34a853;
        font-size: 14px;
        font-weight: 500;
        opacity: 0.7;
    }

    @keyframes pulse {
        0%, 100% { opacity: 0.7; }
        50% { opacity: 1; }
    }

    /* Beautiful form field styling */
    .preview-label {
        color: #3c4043;
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 0;
        letter-spacing: 0.25px;
    }

    .preview-field .preview-input {
        margin-top: 0;
        border: 1px solid #dadce0;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .preview-field .preview-input:focus {
        border-color: #6c757d;
        box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.1);
        outline: none;
    }

    .preview-field .form-check {
        margin-top: 8px;
        margin-bottom: 6px;
        padding: 8px 12px;
        background: #ffffff;
        border: 1px solid #e8eaed;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .preview-field .form-check:hover {
        background: #f8f9fa;
        border-color: #dadce0;
    }

    .preview-field .form-check:last-child {
        margin-bottom: 0;
    }

    .preview-field .form-check-input {
        margin-top: 2px;
    }

    .preview-field .form-check-label {
        color: #3c4043;
        font-size: 14px;
        margin-left: 8px;
    }

    /* Drag handle icon styling */
    .drag-handle i {
        font-size: 16px;
        line-height: 1;
    }

    /* Better alignment for input container */
    .field-input-container {
        align-items: flex-start;
    }

    .field-input-container .drag-handle {
        margin-top: 0;
        align-self: flex-start;
    }

    /* Field Edit Modal Styles */
    .field-edit-form .form-label {
        font-size: 12px;
        font-weight: 500;
        margin-bottom: 4px;
    }

    .field-edit-form .form-control,
    .field-edit-form .form-select {
        font-size: 12px;
        padding: 6px 8px;
    }

    /* Beautiful Style Customization Controls */
    .style-controls {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .style-header {
        padding-bottom: 16px;
        border-bottom: 2px solid #f1f3f4;
        margin-bottom: 20px;
    }

    .style-header h6 {
        font-size: 16px;
        font-weight: 700;
        color: #1a73e8;
        margin-bottom: 4px;
    }

    .style-header small {
        font-size: 12px;
        color: #5f6368;
    }

    .style-section {
        background: #ffffff;
        border: 1px solid #f1f3f4;
        border-radius: 12px;
        padding: 16px;
        transition: all 0.3s ease;
    }

    .style-section:hover {
        border-color: #e8eaff;
        box-shadow: 0 2px 8px rgba(66, 133, 244, 0.08);
    }

    .section-header {
        margin-bottom: 16px;
    }

    .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #3c4043;
        margin-bottom: 0;
        display: flex;
        align-items: center;
    }

    .section-title i {
        color: #4285f4;
        font-size: 16px;
    }

    .section-content {
        position: relative;
    }

    .control-group {
        margin-bottom: 0;
    }

    .control-label {
        font-size: 12px;
        font-weight: 600;
        color: #5f6368;
        margin-bottom: 8px;
        display: block;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Beautiful Color Input Groups */
    .color-input-group {
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 6px;
        transition: all 0.3s ease;
    }

    .color-input-group:hover {
        border-color: #4285f4;
        background: #f8f9ff;
    }

    .color-picker {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .color-picker:hover {
        transform: scale(1.05);
    }

    .color-preview {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        margin: 0 8px;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;
    }

    .color-value {
        font-size: 11px;
        font-weight: 500;
        color: #5f6368;
        font-family: 'Courier New', monospace;
        background: #ffffff;
        padding: 2px 6px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        min-width: 60px;
        text-align: center;
    }

    /* Custom Select Styling */
    .custom-select {
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 12px;
        background: #ffffff;
        transition: all 0.3s ease;
    }

    .custom-select:focus {
        border-color: #4285f4;
        box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
    }

    /* Transparent Toggle */
    .transparent-toggle {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .transparent-toggle:hover {
        border-color: #4285f4;
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    }

    .transparent-toggle input[type="checkbox"] {
        transform: scale(1.2);
        margin-right: 12px;
    }

    .transparent-toggle .toggle-text {
        font-weight: 600;
        color: #3c4043;
        display: block;
    }

    .transparent-toggle .toggle-desc {
        color: #5f6368;
        font-size: 11px;
        display: block;
        margin-top: 2px;
    }

    .transparent-toggle input:checked ~ label .toggle-text {
        color: #4285f4;
    }

    /* Button Position Groups */
    .button-position-group {
        display: flex;
        gap: 4px;
        background: #f8f9fa;
        padding: 4px;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .button-position-group .btn {
        flex: 1;
        font-size: 11px;
        padding: 6px 8px;
        border: none;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .button-position-group .btn:hover {
        background: #e3f2fd;
        color: #1976d2;
    }

    .button-position-group .btn-check:checked + .btn {
        background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
        color: white;
        box-shadow: 0 2px 4px rgba(66, 133, 244, 0.3);
    }

    /* Spacing Options */
    .spacing-options {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
    }

    .spacing-options .btn {
        flex: 1;
        min-width: 70px;
        font-size: 11px;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .spacing-options .btn:hover {
        background: #e3f2fd;
        color: #1976d2;
        border-color: #4285f4;
    }

    .spacing-options .btn-check:checked + .btn {
        background: linear-gradient(135deg, #34a853 0%, #137333 100%);
        color: white;
        border-color: #34a853;
        box-shadow: 0 2px 4px rgba(52, 168, 83, 0.3);
    }

    /* Style Action Buttons */
    .style-action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 8px;
    }

    .style-save-btn,
    .style-reset-btn {
        flex: 1;
        background: #ffffff;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }

    .style-save-btn:hover,
    .style-reset-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-content {
        display: flex;
        align-items: center;
        padding: 14px 16px;
        gap: 12px;
    }

    .btn-content i {
        font-size: 20px;
        flex-shrink: 0;
        width: 24px;
        text-align: center;
    }

    .btn-text-content {
        flex: 1;
        text-align: left;
    }

    .btn-title {
        display: block;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        margin-bottom: 2px;
    }

    .btn-subtitle {
        display: block;
        font-size: 11px;
        opacity: 0.7;
        line-height: 1.2;
    }

    /* Save Button Styling */
    .style-save-btn {
        border-color: #4285f4;
        background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
        color: white;
    }

    .style-save-btn:hover {
        background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
        border-color: #1a73e8;
        box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
    }

    .style-save-btn .btn-content i {
        color: white;
    }

    /* Reset Button Styling */
    .style-reset-btn {
        border-color: #dadce0;
        background: #ffffff;
        color: #5f6368;
    }

    .style-reset-btn:hover {
        background: #f8f9fa;
        border-color: #bdc1c6;
        color: #3c4043;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .style-reset-btn .btn-content i {
        color: #5f6368;
    }

    .style-reset-btn:hover .btn-content i {
        color: #3c4043;
    }

    /* Button States */
    .style-save-btn:disabled,
    .style-reset-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none !important;
    }

    .style-save-btn:disabled:hover,
    .style-reset-btn:disabled:hover {
        transform: none !important;
        box-shadow: none !important;
    }

    /* Loading Animation */
    .ti-spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Subsection Content Styling */
    .subsection .section-content {
        position: relative;
    }

    /* Visual Separator Between Subsections */
    .add-existing-field-subsection::after {
        content: "";
        position: absolute;
        bottom: -10px;
        left: 20px;
        right: 20px;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, #e8eaff 20%, #e8eaff 80%, transparent 100%);
    }

    /* Enhanced Subsection Hover Effects */
    .add-existing-field-subsection:hover {
        border-left-color: #1a73e8;
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    }

    .create-field-subsection:hover {
        border-left-color: #137333;
        background: linear-gradient(135deg, #f8fff8 0%, #ffffff 100%);
    }

    /* Options Builder */
    .options-builder {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
    }

    .option-item {
        margin-bottom: 8px;
    }

    .option-item:last-child {
        margin-bottom: 0;
    }

    .option-item input {
        font-size: 13px;
    }

    .option-item .btn {
        min-width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .add-new-option {
        margin-top: 8px;
        font-size: 12px;
        padding: 6px 12px;
    }

    /* Required Toggle */
    .required-toggle {
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .required-toggle:hover {
        border-color: #4285f4;
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    }

    .required-toggle input[type="checkbox"] {
        transform: scale(1.2);
        margin-right: 12px;
    }

    .required-toggle .toggle-text {
        font-weight: 600;
        color: #3c4043;
        display: block;
    }

    .required-toggle .toggle-desc {
        color: #5f6368;
        font-size: 11px;
        display: block;
        margin-top: 2px;
    }

    .required-toggle input:checked ~ label .toggle-text {
        color: #4285f4;
    }

    /* Create Field Button */
    .create-field-btn {
        background: linear-gradient(135deg, #34a853 0%, #137333 100%);
        border: none;
        border-radius: 8px;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(52, 168, 83, 0.2);
    }

    .create-field-btn:hover {
        background: linear-gradient(135deg, #137333 0%, #0d5025 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(52, 168, 83, 0.3);
    }

    .create-field-btn:active {
        transform: translateY(0);
    }

    /* Field Type Select with Icons */
    #new-field-type option {
        padding: 8px 12px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .style-action-buttons {
            flex-direction: column;
            gap: 8px;
        }

        .button-position-group,
        .spacing-options {
            flex-wrap: wrap;
        }

        .color-input-group {
            flex-wrap: wrap;
            gap: 8px;
        }

        .btn-content {
            padding: 12px 14px;
            gap: 10px;
        }

        .btn-content i {
            font-size: 18px;
        }

        .btn-title {
            font-size: 13px;
        }

        .btn-subtitle {
            font-size: 10px;
        }
    }

    .style-section {
        margin-bottom: 15px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    .style-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .style-section h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 10px;
        font-size: 14px;
    }

    .compact-row {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 8px;
    }

    .compact-row label {
        font-size: 12px;
        margin-bottom: 0;
        min-width: 80px;
    }

    .compact-row .form-control,
    .compact-row .form-select {
        font-size: 12px;
        padding: 4px 8px;
        height: auto;
    }

    .compact-row .form-control-color {
        width: 40px;
        height: 30px;
        padding: 2px;
    }

    .form-check-input {
        margin-top: 0;
    }

    .btn-sm-compact {
        padding: 4px 8px;
        font-size: 11px;
    }

    .preview-small {
        transform: scale(0.8);
        transform-origin: top left;
    }

    .add-field-section {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 15px;
    }

    /* Field Management Section */
    .field-management-section {
        position: relative;
    }

    .main-section-header {
        padding-bottom: 20px;
        border-bottom: 3px solid #f1f3f4;
        margin-bottom: 24px;
        background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        border-radius: 12px;
        padding: 16px 20px;
        border: 1px solid #e8eaff;
    }

    .main-section-title {
        font-size: 18px;
        font-weight: 700;
        color: #1a73e8;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
    }

    .main-section-title i {
        color: #4285f4;
        font-size: 20px;
    }

    /* Subsection Styling */
    .subsection {
        background: #ffffff;
        border: 1px solid #f1f3f4;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        position: relative;
    }

    .subsection:hover {
        border-color: #e8eaff;
        box-shadow: 0 2px 8px rgba(66, 133, 244, 0.08);
    }

    .subsection-header {
        padding-bottom: 16px;
        border-bottom: 2px solid #f8f9fa;
        margin-bottom: 20px;
    }

    .subsection-title {
        font-size: 16px;
        font-weight: 600;
        color: #3c4043;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
    }

    .subsection-title i {
        color: #4285f4;
        font-size: 18px;
    }

    /* Add Existing Field Subsection */
    .add-existing-field-subsection {
        border-left: 4px solid #4285f4;
    }

    /* Create New Field Subsection */
    .create-field-subsection {
        border-left: 4px solid #34a853;
    }

    .create-field-subsection .subsection-title i {
        color: #34a853;
    }

    /* Custom Field Selector */
    .custom-field-selector {
        background: #ffffff;
        border: 1px solid #f1f3f4;
        border-radius: 12px;
        padding: 16px;
        transition: all 0.3s ease;
    }

    .custom-field-selector:hover {
        border-color: #e8eaff;
        box-shadow: 0 2px 8px rgba(66, 133, 244, 0.08);
    }

    .selector-container {
        display: flex;
        gap: 12px;
        align-items: flex-end;
    }

    .select-wrapper {
        flex: 1;
    }

    .select-label {
        font-size: 12px;
        font-weight: 600;
        color: #5f6368;
        margin-bottom: 8px;
        display: block;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .add-button-wrapper {
        flex-shrink: 0;
    }

    /* Add Custom Field Button */
    .add-custom-field-btn {
        background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
        border: none;
        border-radius: 8px;
        padding: 10px 16px;
        color: white;
        font-weight: 600;
        font-size: 13px;
        transition: all 0.3s ease;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(66, 133, 244, 0.2);
        min-width: 120px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .add-custom-field-btn:hover:not(:disabled) {
        background: linear-gradient(135deg, #1a73e8 0%, #1557b0 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(66, 133, 244, 0.3);
    }

    .add-custom-field-btn:disabled {
        background: #f1f3f4;
        color: #9aa0a6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }



    /* Select2 Custom Styling */
    .select2-container--default .select2-selection--single {
        height: 40px;
        border: 1px solid #dadce0;
        border-radius: 8px;
        background: #ffffff;
        transition: all 0.3s ease;
    }

    .select2-container--default .select2-selection--single:hover {
        border-color: #4285f4;
    }

    .select2-container--default.select2-container--focus .select2-selection--single {
        border-color: #4285f4;
        box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 38px;
        padding-left: 12px;
        color: #3c4043;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered i {
        font-size: 16px;
    }

    .selected-field-icon {
        display: inline-flex;
        align-items: center;
        margin-right: 8px;
    }

    .selected-field-icon i {
        font-size: 16px !important;
    }

    .selected-field-text {
        display: inline-flex;
        align-items: center;
    }

    .select2-container--default .select2-selection--single .select2-selection__placeholder {
        color: #9aa0a6;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 38px;
        right: 8px;
    }

    .select2-container--default .select2-selection--single .select2-selection__clear {
        cursor: pointer;
        float: right;
        font-weight: bold;
        margin-right: 26px;
        margin-top: 0;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        line-height: 1;
        display: block;
    }



    .select2-dropdown {
        border: 1px solid #dadce0;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .select2-container--default .select2-results__option {
        padding: 12px 16px;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .select2-container--default .select2-results__option--highlighted {
        background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
        color: white;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field {
        border: 1px solid #dadce0;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 14px;
    }

    .select2-container--default .select2-search--dropdown .select2-search__field:focus {
        border-color: #4285f4;
        outline: none;
        box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
    }

    /* Custom Field Option Styling */
    .custom-field-option {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
    }

    .custom-field-option .field-info {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .custom-field-option .field-icon {
        font-size: 16px;
        width: 24px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .custom-field-option .field-icon i {
        font-size: 16px;
    }

    .custom-field-option .field-name {
        font-weight: 500;
        color: #3c4043;
    }

    .custom-field-option .required-badge {
        color: #ea4335;
        font-weight: bold;
        font-size: 12px;
        background: rgba(234, 67, 53, 0.1);
        padding: 1px 4px;
        border-radius: 3px;
        margin-left: 4px;
    }

    .custom-field-option .field-type {
        font-size: 10px;
        color: #5f6368;
        background: #f1f3f4;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 600;
        letter-spacing: 0.5px;
    }

    /* Field Type Option Styling */
    .field-type-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 0;
    }

    .field-type-option .field-type-icon {
        width: 24px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .field-type-option .field-type-icon i {
        font-size: 16px;
    }

    .field-type-option .field-type-name {
        font-weight: 500;
        color: #3c4043;
    }

    /* Responsive Design for Add Fields */
    @media (max-width: 768px) {
        .selector-container {
            flex-direction: column;
            gap: 12px;
        }

        .add-button-wrapper {
            width: 100%;
        }

        .add-custom-field-btn {
            width: 100%;
            min-width: auto;
        }
    }

    /* Duplicate Field Highlight Animation */
    .field-duplicate-highlight {
        background: linear-gradient(135deg, #fef7e0 0%, #fff3cd 100%) !important;
        border: 2px solid #f0ad4e !important;
        border-radius: 8px !important;
        box-shadow: 0 0 20px rgba(240, 173, 78, 0.3) !important;
        transform: scale(1.02) !important;
        transition: all 0.3s ease !important;
        animation: duplicateFieldPulse 2s ease-in-out;
        position: relative;
        z-index: 10;
    }

    .field-duplicate-highlight::before {
        content: "⚠️ Duplicate Field";
        position: absolute;
        top: -8px;
        right: -8px;
        background: linear-gradient(135deg, #f0ad4e 0%, #ec971f 100%);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        animation: duplicateFieldBadge 2s ease-in-out;
    }

    @keyframes duplicateFieldPulse {
        0%, 100% {
            transform: scale(1.02);
            box-shadow: 0 0 20px rgba(240, 173, 78, 0.3);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 30px rgba(240, 173, 78, 0.5);
        }
    }

    @keyframes duplicateFieldBadge {
        0% {
            opacity: 0;
            transform: scale(0.5) rotate(-10deg);
        }
        20% {
            opacity: 1;
            transform: scale(1.1) rotate(5deg);
        }
        100% {
            opacity: 1;
            transform: scale(1) rotate(0deg);
        }
    }
</style>
@endpush

@push('script-page')
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    let currentFormId = {{ $form->id ?? 0 }};
    let currentStyles = {
        backgroundColor: '#ffffff',
        backgroundTransparent: false,
        inputBackgroundColor: '#ffffff',
        buttonBackgroundColor: '#007bff',
        labelColor: '#212529',
        inputColor: '#495057',
        buttonColor: '#ffffff',
        buttonPosition: 'center',
        fontFamily: 'Arial, sans-serif',
        fontSize: '14px',
        borderColor: '#ced4da',
        borderRadius: '4px',
        customBorderRadius: '4px',
        formTitlePosition: 'center',
        fieldSpacing: '15px',
        hideFormTitle: false
    };

    console.log('Form customization initialized for form ID:', currentFormId);

    // Debug custom fields
    const customFieldOptions = $('#custom-field-select option').length;
    console.log('Custom field options available:', customFieldOptions);

    // Initialize drag and drop
    try {
        initializeDragDrop();
    } catch (e) {
        console.error('Error initializing drag and drop:', e);
    }

    // Load existing styles
    loadFormStyles();

    // Initialize Select2 for font family dropdown
    $('#font-family').select2({
        placeholder: 'Select a font family',
        allowClear: false,
        width: '100%'
    });

    // Handle field type change - Dynamic form sections
    $('#new-field-type').off('change').on('change', function() {
        const fieldType = $(this).val();
        const placeholderSection = $('#placeholder-section');
        const optionsSection = $('#options-section');

        // Show/hide placeholder section based on field type
        if(['text', 'email', 'number', 'textarea'].includes(fieldType)) {
            placeholderSection.show();
        } else {
            placeholderSection.hide();
            $('#new-field-placeholder').val(''); // Clear placeholder when hidden
        }

        // Show/hide options section based on field type
        if(['select', 'radio', 'checkbox', 'multiselect'].includes(fieldType)) {
            optionsSection.show();
        } else {
            optionsSection.hide();
            // Clear options when hidden
            $('#new-field-options-list .option-item input').val('');
        }
    });

    // Trigger change on page load to set initial state
    $('#new-field-type').trigger('change');

    // Handle add new option button
    $(document).off('click', '.add-new-option').on('click', '.add-new-option', function() {
        const optionsList = $('#new-field-options-list');
        const optionCount = optionsList.find('.option-item').length + 1;

        const newOption = `
            <div class="option-item d-flex mb-2">
                <input type="text" class="form-control me-2" placeholder="Option ${optionCount}" value="">
                <button type="button" class="btn btn-sm btn-outline-danger remove-new-option">
                    <i class="ti ti-trash"></i>
                </button>
            </div>
        `;

        optionsList.append(newOption);
        updateRemoveButtonStates();
    });

    // Handle remove option button
    $(document).off('click', '.remove-new-option').on('click', '.remove-new-option', function() {
        $(this).closest('.option-item').remove();
        updateRemoveButtonStates();
        updateOptionPlaceholders();
    });

    // Update remove button states (disable if only one option)
    function updateRemoveButtonStates() {
        const optionItems = $('#new-field-options-list .option-item');
        const removeButtons = optionItems.find('.remove-new-option');

        if (optionItems.length <= 1) {
            removeButtons.prop('disabled', true);
        } else {
            removeButtons.prop('disabled', false);
        }
    }

    // Update option placeholders
    function updateOptionPlaceholders() {
        $('#new-field-options-list .option-item').each(function(index) {
            $(this).find('input').attr('placeholder', `Option ${index + 1}`);
        });
    }

    // Initialize remove button states
    updateRemoveButtonStates();

    // Initialize Select2 for custom field selector
    initializeCustomFieldSelect2();

    // Initialize Select2 for field type selector
    initializeFieldTypeSelect2();

    // Color picker change events
    $('#bg-color').on('change', function() {
        const color = $(this).val();
        currentStyles.backgroundColor = color;
        updateColorPreview('#bg-color', '#bg-preview', color);
        updatePreview();
    });

    $('#bg-transparent').on('change', function() {
        currentStyles.backgroundTransparent = $(this).is(':checked');
        updatePreview();
    });

    $('#input-bg-color').on('change', function() {
        const color = $(this).val();
        currentStyles.inputBackgroundColor = color;
        updateColorPreview('#input-bg-color', '#input-bg-preview', color);
        updatePreview();
    });

    $('#btn-bg-color').on('change', function() {
        const color = $(this).val();
        currentStyles.buttonBackgroundColor = color;
        updateColorPreview('#btn-bg-color', '#btn-bg-preview', color);
        updatePreview();
    });

    $('#btn-color').on('change', function() {
        const color = $(this).val();
        currentStyles.buttonColor = color;
        updateColorPreview('#btn-color', '#btn-color-preview', color);
        updatePreview();
    });

    $('input[name="btn-position"]').on('change', function() {
        currentStyles.buttonPosition = $(this).val();
        updatePreview();
    });

    $('input[name="field-spacing"]').on('change', function() {
        currentStyles.fieldSpacing = $(this).val();
        updatePreview();
    });

    $('#label-color').on('change', function() {
        const color = $(this).val();
        currentStyles.labelColor = color;
        updateColorPreview('#label-color', '#label-preview', color);
        updatePreview();
    });

    $('#input-color').on('change', function() {
        const color = $(this).val();
        currentStyles.inputColor = color;
        updateColorPreview('#input-color', '#input-color-preview', color);
        updatePreview();
    });

    $('#border-color').on('change', function() {
        const color = $(this).val();
        currentStyles.borderColor = color;
        updateColorPreview('#border-color', '#border-preview', color);
        updatePreview();
    });

    $('#font-family').on('change', function() {
        currentStyles.fontFamily = $(this).val();
        updatePreview();
    });

    $('#font-size').on('change', function() {
        currentStyles.fontSize = $(this).val();
        updatePreview();
    });

    // Border radius now handled by custom-border-radius slider

    $('#field-spacing').on('change', function() {
        currentStyles.fieldSpacing = $(this).val();
        updatePreview();
    });

    $('#hide-form-title').on('change', function() {
        currentStyles.hideFormTitle = $(this).is(':checked');
        updatePreview();
    });

    // Custom Border Radius
    $('#custom-border-radius').on('input', function() {
        const value = $(this).val();
        currentStyles.customBorderRadius = value + 'px';
        currentStyles.borderRadius = value + 'px'; // Update both for compatibility
        $('#border-radius-value').text(value + 'px');
        updatePreview();
    });

    // Form Title Position
    $('input[name="title-position"]').on('change', function() {
        currentStyles.formTitlePosition = $(this).val();
        updatePreview();
    });

    // Add custom field from select
    $('#add-custom-field-btn').off('click').on('click', function() {
        console.log('Add custom field button clicked');

        const fieldId = $('#custom-field-select').val();
        const fieldText = $('#custom-field-select option:selected').text();

        console.log('Custom field data:', {fieldId, fieldText});

        if(!fieldId) {
            if (typeof show_toastr === 'function') {
                show_toastr('error', 'Please select a custom field');
            } else {
                alert('Please select a custom field');
            }
            return;
        }

        // Check if this custom field is already added to the form (by label name only)
        console.log(`Checking for duplicate field label: "${fieldText}"`);
        console.log('Current form field labels:');
        $('#sortable-fields .preview-field-container').each(function(index) {
            const existingLabel = $(this).find('.preview-label').text().replace('*', '').trim();
            console.log(`  ${index + 1}. "${existingLabel}"`);
        });

        const duplicateCheck = checkFieldDuplicateWithMessage(fieldText, 'add_existing');
        if(duplicateCheck.isDuplicate) {
            console.log(`Duplicate label detected: "${fieldText}"`);
            show_toastr('error', duplicateCheck.message);

            // Highlight existing field in the form
            highlightExistingField(fieldText);
            return;
        }

        console.log(`No duplicate label found for: "${fieldText}", proceeding with field addition`);

        // Show confirmation dialog
        if(confirm('Add "' + fieldText + '" field to this form?')) {
            const requestData = {
                custom_field_id: fieldId,
                required: 0,
                _token: '{{ csrf_token() }}'
            };

            console.log('Sending custom field request:', requestData);

            $.ajax({
                url: `/form_builder/${currentFormId}/add_custom_field`,
                method: 'POST',
                data: requestData,
                beforeSend: function() {
                    console.log('Custom field AJAX request started');
                },
                success: function(response) {
                    console.log('Custom field response:', response);
                    if(response.success) {
                        if (typeof show_toastr === 'function') {
                            show_toastr('success', response.message);
                        } else {
                            alert(response.message);
                        }
                        // Clear the selection
                        $('#custom-field-select').val('');
                        // Reload the page to show the new field
                        location.reload();
                    } else {
                        if (typeof show_toastr === 'function') {
                            show_toastr('error', response.message);
                        } else {
                            alert(response.message);
                        }
                    }
                },
                error: function(xhr) {
                    console.error('Custom field error:', xhr);
                    const errorMessage = xhr.responseJSON?.message || 'Error adding custom field';
                    if (typeof show_toastr === 'function') {
                        show_toastr('error', errorMessage);
                    } else {
                        alert(errorMessage);
                    }
                }
            });
        }
    });

    // Handle create field form submission
    $('#create-field-form').off('submit').on('submit', function(e) {
        e.preventDefault();
        console.log('Create field form submitted');

        const fieldName = $('#new-field-name').val().trim();
        const fieldType = $('#new-field-type').val();
        const fieldPlaceholder = $('#new-field-placeholder').val().trim();
        const fieldRequired = $('#new-field-required').is(':checked');

        // Validation
        if(!fieldName) {
            show_toastr('error', 'Please enter field name');
            $('#new-field-name').focus();
            return;
        }

        // Check if field name already exists in current form or custom fields
        const duplicateCheck = checkFieldDuplicateWithMessage(fieldName, 'create');
        if(duplicateCheck.isDuplicate) {
            show_toastr('error', duplicateCheck.message);
            $('#new-field-name').focus().select();

            // Highlight existing field if it's in the form
            highlightExistingField(fieldName);
            return;
        }

        // Collect options for choice fields
        let optionsArray = [];
        if(['select', 'radio', 'checkbox', 'multiselect'].includes(fieldType)) {
            $('#new-field-options-list .option-item input').each(function() {
                const value = $(this).val().trim();
                if(value) {
                    optionsArray.push(value);
                }
            });

            if(optionsArray.length === 0) {
                show_toastr('error', 'Please enter at least one option for this field type');
                return;
            }
        }

        // Prepare form data in the exact format the fieldStore method expects
        const formData = new FormData();
        formData.append('name[]', fieldName);
        formData.append('type[]', fieldType);
        formData.append('placeholder[]', fieldPlaceholder);
        formData.append('required[]', fieldRequired ? 1 : 0);
        formData.append('_token', '{{ csrf_token() }}');

        // Add options if applicable
        if(optionsArray.length > 0) {
            optionsArray.forEach(option => {
                formData.append('options[]', option);
            });
        }

        // Disable submit button and show loading
        const submitBtn = $('#add-new-field-btn');
        const originalContent = submitBtn.html();
        submitBtn.prop('disabled', true).html('<i class="ti ti-loader ti-spin me-2"></i>Creating...');

        console.log('Sending AJAX request to create field:', {fieldName, fieldType, fieldPlaceholder, fieldRequired, optionsArray});

            $.ajax({
                url: `/form_builder/${currentFormId}/field`,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function() {
                    console.log('AJAX request started');
                },
                success: function(response) {
                    console.log('Field creation response:', response);
                    show_toastr('success', 'Field created successfully and added to custom fields library');

                    // Add the new field to the bottom of the form preview
                    if(response.field && response.field.id) {
                        addNewFieldToPreview(response.field);
                    } else {
                        // Fallback: reload page if no field data returned
                        location.reload();
                        return;
                    }

                    // Update the custom fields dropdown with new fields
                    if(response.customFields) {
                        updateCustomFieldsDropdown(response.customFields);
                    }

                    // Clear and reset form
                    clearCreateFieldForm();

                    // Re-enable submit button
                    submitBtn.prop('disabled', false).html(originalContent);
                },
                error: function(xhr) {
                    console.error('Field creation error:', xhr);
                    console.error('Response text:', xhr.responseText);
                    const errorMessage = xhr.responseJSON?.message || 'Error creating field';
                    show_toastr('error', errorMessage);

                    // Re-enable submit button
                    submitBtn.prop('disabled', false).html(originalContent);
                }
            });
    });

    // Function to add new field to preview (at the bottom)
    function addNewFieldToPreview(fieldData) {
        const fieldId = fieldData.id;
        const fieldName = fieldData.name;
        const fieldType = fieldData.type;
        const fieldPlaceholder = fieldData.placeholder || '';
        const fieldRequired = fieldData.required;
        const fieldOptions = fieldData.options || [];

        // Generate field HTML
        let fieldInputHtml = '';
        switch(fieldType) {
            case 'text':
            case 'email':
                fieldInputHtml = `<input type="${fieldType}" class="preview-input form-control" placeholder="${fieldPlaceholder || 'Enter ' + fieldName.toLowerCase()}">`;
                break;
            case 'number':
                // Check if it's a phone/contact field
                const isPhoneField = fieldName.toLowerCase().includes('contact') ||
                                   fieldName.toLowerCase().includes('phone') ||
                                   fieldName.toLowerCase().includes('mobile');

                if (isPhoneField) {
                    fieldInputHtml = `
                        <div class="phone-input-container">
                            <div class="country-selector">
                                <button type="button" class="country-dropdown-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="flag">🇮🇳</span>
                                    <span class="code">+91</span>
                                    <i class="ti ti-chevron-down"></i>
                                </button>
                                <ul class="dropdown-menu country-dropdown">
                                    <li><a class="dropdown-item" href="#" data-country="IN" data-code="+91" data-flag="🇮🇳">🇮🇳 India (+91)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="US" data-code="+1" data-flag="🇺🇸">🇺🇸 United States (+1)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="GB" data-code="+44" data-flag="🇬🇧">🇬🇧 United Kingdom (+44)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="CN" data-code="+86" data-flag="🇨🇳">🇨🇳 China (+86)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="JP" data-code="+81" data-flag="🇯🇵">🇯🇵 Japan (+81)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="DE" data-code="+49" data-flag="🇩🇪">🇩🇪 Germany (+49)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="FR" data-code="+33" data-flag="🇫🇷">🇫🇷 France (+33)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="IT" data-code="+39" data-flag="🇮🇹">🇮🇹 Italy (+39)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="ES" data-code="+34" data-flag="🇪🇸">🇪🇸 Spain (+34)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="RU" data-code="+7" data-flag="🇷🇺">🇷🇺 Russia (+7)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="BR" data-code="+55" data-flag="🇧🇷">🇧🇷 Brazil (+55)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="AU" data-code="+61" data-flag="🇦🇺">🇦🇺 Australia (+61)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="KR" data-code="+82" data-flag="🇰🇷">🇰🇷 South Korea (+82)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="SG" data-code="+65" data-flag="🇸🇬">🇸🇬 Singapore (+65)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="MY" data-code="+60" data-flag="🇲🇾">🇲🇾 Malaysia (+60)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="TH" data-code="+66" data-flag="🇹🇭">🇹🇭 Thailand (+66)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="VN" data-code="+84" data-flag="🇻🇳">🇻🇳 Vietnam (+84)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="ID" data-code="+62" data-flag="🇮🇩">🇮🇩 Indonesia (+62)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="PH" data-code="+63" data-flag="🇵🇭">🇵🇭 Philippines (+63)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="AE" data-code="+971" data-flag="🇦🇪">🇦🇪 UAE (+971)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="SA" data-code="+966" data-flag="🇸🇦">🇸🇦 Saudi Arabia (+966)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="ZA" data-code="+27" data-flag="🇿🇦">🇿🇦 South Africa (+27)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="EG" data-code="+20" data-flag="🇪🇬">🇪🇬 Egypt (+20)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="MX" data-code="+52" data-flag="🇲🇽">🇲🇽 Mexico (+52)</a></li>
                                    <li><a class="dropdown-item" href="#" data-country="AR" data-code="+54" data-flag="🇦🇷">🇦🇷 Argentina (+54)</a></li>
                                </ul>
                            </div>
                            <input type="number" class="preview-input form-control phone-number-input" placeholder="${fieldPlaceholder || 'Enter ' + fieldName.toLowerCase()}">
                        </div>
                    `;
                } else {
                    fieldInputHtml = `<input type="number" class="preview-input form-control" placeholder="${fieldPlaceholder || 'Enter ' + fieldName.toLowerCase()}">`;
                }
                break;
            case 'textarea':
                fieldInputHtml = `<textarea class="preview-input form-control" rows="3" placeholder="${fieldPlaceholder || 'Enter ' + fieldName.toLowerCase()}"></textarea>`;
                break;
            case 'select':
                fieldInputHtml = `
                    <select class="preview-input form-control">
                        <option>${fieldPlaceholder || 'Select ' + fieldName.toLowerCase()}</option>
                        ${fieldOptions.map(option => `<option>${option}</option>`).join('')}
                    </select>
                `;
                break;
            case 'multiselect':
                fieldInputHtml = `
                    <select class="preview-input form-control" multiple>
                        ${fieldOptions.map(option => `<option>${option}</option>`).join('')}
                    </select>
                `;
                break;
            case 'radio':
                fieldInputHtml = fieldOptions.map(option => `
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="radio_${fieldId}" value="${option}">
                        <label class="form-check-label">${option}</label>
                    </div>
                `).join('');
                break;
            case 'checkbox':
                fieldInputHtml = fieldOptions.map(option => `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${option}">
                        <label class="form-check-label">${option}</label>
                    </div>
                `).join('');
                break;
            case 'date':
                fieldInputHtml = `<input type="date" class="preview-input form-control">`;
                break;
            case 'file':
                fieldInputHtml = `<input type="file" class="preview-input form-control">`;
                break;
            default:
                fieldInputHtml = `<input type="text" class="preview-input form-control" placeholder="${fieldPlaceholder || 'Enter ' + fieldName.toLowerCase()}">`;
        }

        // Create complete field HTML
        const requiredStar = fieldRequired ? ' <span class="text-danger">*</span>' : '';
        const fieldHtml = `
            <div class="preview-field-container mb-3" data-field-id="${fieldId}" data-field-type="${fieldType}">
                <div class="preview-field">
                    <div class="field-header d-flex align-items-center justify-content-between mb-2">
                        <label class="preview-label form-label fw-medium mb-0">${fieldName}${requiredStar}</label>
                        <div class="field-actions">
                            <button type="button" class="btn btn-sm btn-outline-primary edit-field-btn" data-field-id="${fieldId}" title="Edit Field">
                                <i class="ti ti-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-field-btn" data-field-id="${fieldId}" title="Delete Field">
                                <i class="ti ti-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="field-input-container d-flex align-items-center">
                        <div class="drag-handle me-2">
                            <i class="ti ti-grip-vertical text-muted"></i>
                        </div>
                        <div class="field-input flex-grow-1">
                            ${fieldInputHtml}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add to the bottom of the sortable fields container
        $('#sortable-fields').append(fieldHtml);

        // Re-initialize drag and drop for the new field
        initializeDragDrop();

        console.log(`New field "${fieldName}" added to preview at bottom`);
    }

    // Function to clear create field form
    function clearCreateFieldForm() {
        $('#new-field-name').val('');
        $('#new-field-type').val('text').trigger('change'); // Trigger change to reset sections
        $('#new-field-placeholder').val('');
        $('#new-field-required').prop('checked', false);

        // Reset options to default state
        const optionsList = $('#new-field-options-list');
        optionsList.html(`
            <div class="option-item d-flex mb-2">
                <input type="text" class="form-control me-2" placeholder="Option 1" value="">
                <button type="button" class="btn btn-sm btn-outline-danger remove-new-option" disabled>
                    <i class="ti ti-trash"></i>
                </button>
            </div>
            <div class="option-item d-flex mb-2">
                <input type="text" class="form-control me-2" placeholder="Option 2" value="">
                <button type="button" class="btn btn-sm btn-outline-danger remove-new-option">
                    <i class="ti ti-trash"></i>
                </button>
            </div>
        `);

        updateRemoveButtonStates();
        console.log('Create field form cleared');
    }

    // Check if field name already exists - different logic for create vs add existing
    function isFieldNameDuplicate(fieldName, returnDetails = false, context = 'create') {
        const normalizedFieldName = fieldName.toLowerCase().trim();

        // For adding existing fields: only check current form
        if (context === 'add_existing') {
            let duplicateInForm = false;
            let existingFieldElement = null;

            $('#sortable-fields .preview-field-container').each(function() {
                const $container = $(this);
                const $label = $container.find('.preview-label');

                // Get the actual label text (what user sees)
                const labelText = $label.text().replace('*', '').trim();
                const normalizedExisting = labelText.toLowerCase();

                console.log(`Comparing form field: "${normalizedExisting}" vs "${normalizedFieldName}"`);

                if (normalizedExisting === normalizedFieldName) {
                    duplicateInForm = true;
                    existingFieldElement = $container;
                    return false; // Break the loop
                }
            });

            if (duplicateInForm) {
                console.log(`Duplicate field found in current form: "${fieldName}"`);
                if (returnDetails) {
                    return {
                        isDuplicate: true,
                        location: 'form',
                        element: existingFieldElement,
                        message: 'This field is already added to the form. Each field can only be added once.'
                    };
                }
                return true;
            }

            console.log(`Field "${fieldName}" not found in current form - OK to add`);
            if (returnDetails) {
                return {
                    isDuplicate: false,
                    message: 'Field not in current form.'
                };
            }
            return false;
        }

        // For creating new fields: check both current form AND custom fields table
        let duplicateInForm = false;
        let existingFieldElement = null;

        $('#sortable-fields .preview-field-container').each(function() {
            const $container = $(this);
            const $label = $container.find('.preview-label');

            const labelText = $label.text().replace('*', '').trim();
            const normalizedExisting = labelText.toLowerCase();

            if (normalizedExisting === normalizedFieldName) {
                duplicateInForm = true;
                existingFieldElement = $container;
                return false; // Break the loop
            }
        });

        if (duplicateInForm) {
            console.log(`Duplicate field label found in form: "${fieldName}"`);
            if (returnDetails) {
                return {
                    isDuplicate: true,
                    location: 'form',
                    element: existingFieldElement,
                    message: 'This field name already exists in the current form.'
                };
            }
            return true;
        }

        // Check existing custom fields (only when creating new fields, not when adding existing ones)
        let duplicateInCustomFields = false;
        $('#custom-field-select option').each(function() {
            if ($(this).val()) { // Skip empty option
                const customFieldName = $(this).text().trim().toLowerCase();
                if (customFieldName === normalizedFieldName) {
                    duplicateInCustomFields = true;
                    return false; // Break the loop
                }
            }
        });

        if (duplicateInCustomFields) {
            console.log(`Duplicate field name found in custom fields: "${fieldName}"`);
            if (returnDetails) {
                return {
                    isDuplicate: true,
                    location: 'custom_fields',
                    message: 'A custom field with this name already exists. Please choose a different name.'
                };
            }
            return true;
        }

        console.log(`Field name "${fieldName}" is unique`);
        if (returnDetails) {
            return {
                isDuplicate: false,
                message: 'Field name is unique.'
            };
        }
        return false;
    }

    // Enhanced duplicate check with specific error messages
    function checkFieldDuplicateWithMessage(fieldName, context = 'create') {
        const result = isFieldNameDuplicate(fieldName, true, context);

        if (result.isDuplicate) {
            return {
                isDuplicate: true,
                message: result.message
            };
        }

        return {
            isDuplicate: false,
            message: result.message
        };
    }

    // Highlight existing field in the form when duplicate is detected
    function highlightExistingField(fieldName) {
        const normalizedFieldName = fieldName.toLowerCase().trim();

        $('#sortable-fields .preview-field-container').each(function() {
            const existingFieldName = $(this).find('.preview-label').text().replace('*', '').trim().toLowerCase();
            if (existingFieldName === normalizedFieldName) {
                const $fieldContainer = $(this);

                // Add highlight class
                $fieldContainer.addClass('field-duplicate-highlight');

                // Scroll to the field
                $fieldContainer[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Remove highlight after 3 seconds
                setTimeout(function() {
                    $fieldContainer.removeClass('field-duplicate-highlight');
                }, 3000);

                return false; // Break the loop
            }
        });
    }

    // Save styles
    $('#save-styles').off('click').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $btn = $(this);

        // Prevent double submission
        if ($btn.prop('disabled') || $btn.hasClass('saving')) {
            return false;
        }

        const $btnContent = $btn.find('.btn-content');
        const originalContent = $btnContent.html();

        // Show saving state
        $btn.prop('disabled', true).addClass('saving');
        $btnContent.html('<i class="ti ti-loader ti-spin"></i><div class="btn-text-content"><span class="btn-title">{{__("Saving...")}}</span><small class="btn-subtitle">{{__("Please wait")}}</small></div>');

        console.log('Saving styles:', currentStyles);
        console.log('backgroundTransparent value being saved:', currentStyles.backgroundTransparent, 'type:', typeof currentStyles.backgroundTransparent);

        $.ajax({
            url: '/form_builder/save_styles',
            method: 'POST',
            data: {
                form_id: currentFormId,
                styles: currentStyles,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if(response.success) {
                    show_toastr('success', response.message);

                    // Show success state briefly
                    $btnContent.html('<i class="ti ti-check"></i><div class="btn-text-content"><span class="btn-title">{{__("Saved!")}}</span><small class="btn-subtitle">{{__("Changes applied")}}</small></div>');

                    setTimeout(function() {
                        $btnContent.html(originalContent);
                    }, 1500);
                } else {
                    show_toastr('error', response.message);
                    $btnContent.html(originalContent);
                }
            },
            error: function() {
                show_toastr('error', 'Error saving styles');
                $btnContent.html(originalContent);
            },
            complete: function() {
                setTimeout(function() {
                    $btn.prop('disabled', false).removeClass('saving');
                }, 1500);
            }
        });
    });

    // Reset styles
    $('#reset-styles').off('click').on('click', function() {
        if(confirm('Are you sure you want to reset all styles to default?')) {
            currentStyles = {
                backgroundColor: '#ffffff',
                backgroundTransparent: false,
                inputBackgroundColor: '#ffffff',
                buttonBackgroundColor: '#007bff',
                labelColor: '#212529',
                inputColor: '#495057',
                buttonColor: '#ffffff',
                buttonPosition: 'center',
                fontFamily: 'Arial, sans-serif',
                fontSize: '14px',
                borderColor: '#ced4da',
                borderRadius: '4px',
                customBorderRadius: '4px',
                formTitlePosition: 'center',
                fieldSpacing: '15px',
                hideFormTitle: false
            };
            updateFormControls();
            updatePreview();
        }
    });

    function loadFormStyles() {
        $.ajax({
            url: `/form_builder/${currentFormId}/styles`,
            method: 'GET',
            success: function(response) {
                if(response.success && response.styles) {
                    console.log('Loaded styles from server:', response.styles);
                    console.log('backgroundTransparent value:', response.styles.backgroundTransparent, 'type:', typeof response.styles.backgroundTransparent);
                    currentStyles = { ...currentStyles, ...response.styles };
                    console.log('Current styles after merge:', currentStyles);
                    updateFormControls();
                    updatePreview();
                }
            },
            error: function(xhr) {
                console.log('No saved styles found, using defaults');
            }
        });
    }

    function updateFormControls() {
        $('#bg-color').val(currentStyles.backgroundColor || '#ffffff');
        $('#bg-transparent').prop('checked', currentStyles.backgroundTransparent === true);
        $('#input-bg-color').val(currentStyles.inputBackgroundColor || '#ffffff');
        $('#btn-bg-color').val(currentStyles.buttonBackgroundColor || '#007bff');
        $('#btn-color').val(currentStyles.buttonColor || '#ffffff');

        // Update radio buttons for button position
        const buttonPosition = currentStyles.buttonPosition || 'center';
        $(`input[name="btn-position"][value="${buttonPosition}"]`).prop('checked', true);

        $('#label-color').val(currentStyles.labelColor || '#212529');
        $('#input-color').val(currentStyles.inputColor || '#495057');
        $('#border-color').val(currentStyles.borderColor || '#ced4da');
        $('#font-family').val(currentStyles.fontFamily || 'Arial, sans-serif');
        $('#font-size').val(currentStyles.fontSize || '14px');
        // Border radius now handled by custom-border-radius slider

        // Update radio buttons for field spacing
        const fieldSpacing = currentStyles.fieldSpacing || '15px';
        $(`input[name="field-spacing"][value="${fieldSpacing}"]`).prop('checked', true);

        // Update hide form title checkbox
        $('#hide-form-title').prop('checked', currentStyles.hideFormTitle === true);

        // Update custom border radius
        const borderRadiusValue = parseInt(currentStyles.customBorderRadius || currentStyles.borderRadius || '4px');
        $('#custom-border-radius').val(borderRadiusValue);
        $('#border-radius-value').text(borderRadiusValue + 'px');

        // Update radio buttons for form title position
        const titlePosition = currentStyles.formTitlePosition || 'center';
        $(`input[name="title-position"][value="${titlePosition}"]`).prop('checked', true);

        // Update color previews and values
        updateColorPreview('#bg-color', '#bg-preview', currentStyles.backgroundColor || '#ffffff');
        updateColorPreview('#input-bg-color', '#input-bg-preview', currentStyles.inputBackgroundColor || '#ffffff');
        updateColorPreview('#btn-bg-color', '#btn-bg-preview', currentStyles.buttonBackgroundColor || '#007bff');
        updateColorPreview('#btn-color', '#btn-color-preview', currentStyles.buttonColor || '#ffffff');
        updateColorPreview('#label-color', '#label-preview', currentStyles.labelColor || '#212529');
        updateColorPreview('#input-color', '#input-color-preview', currentStyles.inputColor || '#495057');
        updateColorPreview('#border-color', '#border-preview', currentStyles.borderColor || '#ced4da');
    }

    function updateColorPreview(inputId, previewId, color) {
        $(inputId).val(color);
        $(previewId).css('background-color', color);
        $(inputId).closest('.color-input-group').find('.color-value').text(color.toUpperCase());
    }

    function updatePreview() {
        // Remove existing dynamic styles
        $('#dynamic-styles').remove();

        // Handle background transparency
        let bgColor = currentStyles.backgroundColor;
        let boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
        let borderStyle = `1px solid ${currentStyles.borderColor}`;

        if(currentStyles.backgroundTransparent) {
            bgColor = 'transparent';
            boxShadow = 'none';
            borderStyle = 'none';
        }

        // Button position alignment
        let buttonAlignment = 'center';
        if(currentStyles.buttonPosition === 'left') buttonAlignment = 'flex-start';
        if(currentStyles.buttonPosition === 'right') buttonAlignment = 'flex-end';

        // Debug form title position in preview
        console.log('Preview update - formTitlePosition value:', currentStyles.formTitlePosition);
        console.log('Preview update - text-align will be:', currentStyles.formTitlePosition || 'center');

        const styles = `
            <style id="dynamic-styles">
                :root {
                    --input-border-color: ${currentStyles.borderColor || '#ced4da'};
                    --input-border-hover-color: #adb5bd;
                    --input-border-focus-color: #80bdff;
                }
                #form-preview.card {
                    background-color: ${bgColor} !important;
                    box-shadow: ${boxShadow} !important;
                    border: ${borderStyle} !important;
                    border-radius: ${currentStyles.borderRadius} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-size: ${currentStyles.fontSize} !important;
                    width: 100% !important;
                    max-width: 100% !important;
                }
                #form-preview .card-body {
                    background-color: ${bgColor} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-size: ${currentStyles.fontSize} !important;
                    padding: 12px !important;
                    width: 100% !important;
                    box-sizing: border-box !important;
                }
                .preview-field {
                    margin-bottom: ${currentStyles.fieldSpacing} !important;
                }
                .preview-label {
                    color: ${currentStyles.labelColor} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-weight: 500 !important;
                    margin-bottom: 0.5rem !important;
                    display: block !important;
                }
                .preview-input {
                    background-color: ${currentStyles.inputBackgroundColor} !important;
                    color: ${currentStyles.inputColor} !important;
                    border-color: ${currentStyles.borderColor} !important;
                    border-radius: ${currentStyles.borderRadius} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-size: ${currentStyles.fontSize} !important;
                    padding: 0.75rem !important;
                    border: 1px solid !important;
                    width: 100% !important;
                }
                .country-dropdown-btn {
                    border-color: ${currentStyles.borderColor} !important;
                    border: 1px solid !important;
                }
                .preview-button-container {
                    display: flex !important;
                    justify-content: ${buttonAlignment} !important;
                    margin-top: 1.5rem !important;
                }
                .preview-button {
                    background-color: ${currentStyles.buttonBackgroundColor} !important;
                    color: ${currentStyles.buttonColor} !important;
                    border: none !important;
                    border-radius: ${currentStyles.borderRadius} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-size: ${currentStyles.fontSize} !important;
                    padding: 0.75rem 1.5rem !important;
                    cursor: pointer !important;
                    font-weight: 500 !important;
                }
                .preview-title {
                    color: ${currentStyles.labelColor} !important;
                    font-family: ${currentStyles.fontFamily} !important;
                    font-size: 1.5rem !important;
                    font-weight: 600 !important;
                    text-align: ${currentStyles.formTitlePosition || 'center'} !important;
                    margin-bottom: 1.5rem !important;
                    display: ${currentStyles.hideFormTitle ? 'none' : 'block'} !important;
                }
            </style>
        `;

        $('head').append(styles);
    }

    function hexToRgba(hex, alpha) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    // Delete field (prevent duplicate bindings)
    $(document).off('click', '.delete-field-btn').on('click', '.delete-field-btn', function() {
        const fieldId = $(this).data('field-id');

        if(confirm('Are you sure you want to delete this field?')) {
            // Create a form to submit the DELETE request properly
            const form = $('<form>', {
                'method': 'POST',
                'action': `/form_builder/${currentFormId}/field/${fieldId}`
            });

            // Add CSRF token
            form.append($('<input>', {
                'type': 'hidden',
                'name': '_token',
                'value': '{{ csrf_token() }}'
            }));

            // Add method spoofing for DELETE
            form.append($('<input>', {
                'type': 'hidden',
                'name': '_method',
                'value': 'DELETE'
            }));

            // Append form to body and submit
            $('body').append(form);
            form.submit();
        }
    });

    // Edit field (prevent duplicate bindings)
    $(document).off('click', '.edit-field-btn').on('click', '.edit-field-btn', function() {
        const fieldId = $(this).data('field-id');
        openEditFieldModal(fieldId);
    });

    function initializeDragDrop() {
        try {
            if ($("#sortable-fields").length > 0) {
                $("#sortable-fields").sortable({
                    handle: ".drag-handle",
                    placeholder: "ui-sortable-placeholder",
                    tolerance: "pointer",
                    cursor: "grabbing",
                    opacity: 0.9,
                    update: function(event, ui) {
                        const fieldOrder = [];
                        $('#sortable-fields .preview-field-container').each(function(index) {
                            fieldOrder.push({
                                id: $(this).data('field-id'),
                                order: index + 1
                            });
                        });

                        // Save field order
                        $.ajax({
                            url: `/form_builder/${currentFormId}/reorder-fields`,
                            method: 'POST',
                            data: {
                                field_order: fieldOrder,
                                _token: '{{ csrf_token() }}'
                            },
                            success: function(response) {
                                if (typeof show_toastr === 'function') {
                                    show_toastr('success', 'Field order updated');
                                }
                            },
                            error: function() {
                                if (typeof show_toastr === 'function') {
                                    show_toastr('error', 'Error updating field order');
                                }
                            }
                        });
                    }
                });
            }
        } catch (e) {
            console.error('Error initializing sortable:', e);
        }
    }

    function openEditFieldModal(fieldId) {
        // Get field data using existing route
        $.ajax({
            url: `/form_builder/${currentFormId}/field/${fieldId}/edit`,
            method: 'GET',
            success: function(response) {
                // Extract field data from the response HTML or use AJAX to get JSON data
                // For now, let's create the modal and populate it manually
                showEditModal(fieldId);
            },
            error: function() {
                if (typeof show_toastr === 'function') {
                    show_toastr('error', 'Error loading field data');
                } else {
                    alert('Error loading field data');
                }
            }
        });
    }

    function showEditModal(fieldId) {
        // Get field data from the server-side rendered data
        const formFields = @json($form->form_field ?? []);
        const fieldData = formFields.find(field => field.id == fieldId);

        // Fallback to DOM if field data not found
        const fieldContainer = $(`.preview-field-container[data-field-id="${fieldId}"]`);
        const fieldName = fieldData ? fieldData.name : fieldContainer.find('.preview-label').text().replace('*', '').trim();
        const fieldType = fieldData ? fieldData.type : fieldContainer.data('field-type');
        const fieldPlaceholder = fieldData && fieldData.placeholder ? fieldData.placeholder : '';
        const fieldRequired = fieldData ? fieldData.required : fieldContainer.find('.preview-label .text-danger').length > 0;
        const fieldOptions = fieldData ? fieldData.options : [];

        // Create a simple edit modal
        const modalHtml = `
            <div class="modal fade" id="editFieldModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Edit Field</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editFieldForm">
                                <div class="row mb-2">
                                    <div class="col-12">
                                        <label class="form-label">Field Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="edit-field-name" value="${fieldName}" required>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6">
                                        <label class="form-label">Field Type <span class="text-danger">*</span></label>
                                        <select class="form-select" id="edit-field-type" required>
                                            <option value="text" ${fieldType === 'text' ? 'selected' : ''}>Text</option>
                                            <option value="email" ${fieldType === 'email' ? 'selected' : ''}>Email</option>
                                            <option value="number" ${fieldType === 'number' ? 'selected' : ''}>Number</option>
                                            <option value="textarea" ${fieldType === 'textarea' ? 'selected' : ''}>Textarea</option>
                                            <option value="select" ${fieldType === 'select' ? 'selected' : ''}>Select</option>
                                            <option value="radio" ${fieldType === 'radio' ? 'selected' : ''}>Radio</option>
                                            <option value="checkbox" ${fieldType === 'checkbox' ? 'selected' : ''}>Checkbox</option>
                                            <option value="multiselect" ${fieldType === 'multiselect' ? 'selected' : ''}>Multiselect</option>
                                            <option value="date" ${fieldType === 'date' ? 'selected' : ''}>Date</option>
                                            <option value="file" ${fieldType === 'file' ? 'selected' : ''}>File</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label class="form-label">Placeholder</label>
                                        <input type="text" class="form-control" id="edit-field-placeholder" value="${fieldPlaceholder}">
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-12 ps-3">
                                        <div class="form-check d-flex align-items-center">
                                            <input class="form-check-input" type="checkbox" id="edit-field-required" ${fieldRequired ? 'checked' : ''}>
                                            <label class="form-check-label" for="edit-field-required">Required Field</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-2 options-container" style="display:none;">
                                    <div class="col-12">
                                        <label class="form-label">Options</label>
                                        <div class="options-list">
                                            ${Array.isArray(fieldOptions) && fieldOptions.length > 0 ? fieldOptions.map(option => `
                                                <div class="d-flex mb-2">
                                                    <input type="text" class="form-control me-2" value="${option}" placeholder="Option">
                                                    <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                                                </div>
                                            `).join('') : `
                                                <div class="d-flex mb-2">
                                                    <input type="text" class="form-control me-2" placeholder="Option 1">
                                                    <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                                                </div>
                                            `}
                                        </div>
                                        <button type="button" class="btn btn-sm btn-secondary add-option">Add Option</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="save-field-changes" data-field-id="${fieldId}">Save Changes</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal and add new one
        $('#editFieldModal').remove();
        $('body').append(modalHtml);

        // Show modal and bind events
        bindEditModalEvents(fieldId);
    }

    function bindEditModalEvents(fieldId) {
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('editFieldModal'));
        modal.show();

        // Bind save button event
        $('#save-field-changes').off('click').on('click', function() {
            const fieldId = $(this).data('field-id');
            saveFieldEdit(fieldId);
        });

        // Handle field type change in edit modal
        $('#edit-field-type').off('change').on('change', function() {
            const fieldType = $(this).val();
            const optionsContainer = $('.options-container');

            if(['select', 'radio', 'checkbox', 'multiselect'].includes(fieldType)) {
                optionsContainer.show();
            } else {
                optionsContainer.hide();
            }
        });

        // Trigger change to show/hide options
        $('#edit-field-type').trigger('change');

        // Handle add option button (use off to prevent duplicate bindings)
        $(document).off('click', '.add-option').on('click', '.add-option', function() {
            const optionsList = $(this).siblings('.options-list');
            const newOption = `
                <div class="d-flex mb-2">
                    <input type="text" class="form-control me-2" placeholder="Option">
                    <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                </div>
            `;
            optionsList.append(newOption);
        });

        // Handle remove option button (use off to prevent duplicate bindings)
        $(document).off('click', '.remove-option').on('click', '.remove-option', function() {
            $(this).closest('.d-flex').remove();
        });
    }

    function saveFieldEdit(fieldId) {
        const fieldName = $('#edit-field-name').val().trim();
        const fieldType = $('#edit-field-type').val();
        const fieldPlaceholder = $('#edit-field-placeholder').val() ? $('#edit-field-placeholder').val().trim() : '';
        const fieldRequired = $('#edit-field-required').is(':checked');

        console.log('saveFieldEdit - collected data:', {fieldId, fieldName, fieldType, fieldPlaceholder, fieldRequired});

        if(!fieldName) {
            if (typeof show_toastr === 'function') {
                show_toastr('error', 'Please enter field name');
            } else {
                alert('Please enter field name');
            }
            return;
        }

        // Collect options from the options list
        const optionsArray = [];
        $('.options-list input[type="text"]').each(function() {
            const value = $(this).val().trim();
            if(value) {
                optionsArray.push(value);
            }
        });

        // Validate options for select/radio/checkbox/multiselect fields
        if(['select', 'radio', 'checkbox', 'multiselect'].includes(fieldType) && optionsArray.length === 0) {
            if (typeof show_toastr === 'function') {
                show_toastr('error', 'Please enter options for this field type');
            } else {
                alert('Please enter options for this field type');
            }
            return;
        }

        // Prepare form data in the same format as the existing form field update
        const formData = {
            name: fieldName,
            type: fieldType,
            placeholder: fieldPlaceholder,
            required: fieldRequired ? 1 : 0,
            _token: '{{ csrf_token() }}'
        };

        // Add options if applicable
        if(['select', 'radio', 'checkbox', 'multiselect'].includes(fieldType) && optionsArray.length > 0) {
            formData['options[]'] = optionsArray;
        }

        $.ajax({
            url: `/form_builder/${currentFormId}/field/${fieldId}`,
            method: 'POST',
            data: formData,
            success: function(response) {
                if (typeof show_toastr === 'function') {
                    show_toastr('success', 'Field updated successfully');
                } else {
                    alert('Field updated successfully');
                }

                // Update the field in the preview without page reload
                updateFieldInPreview(fieldId, fieldName, fieldType, fieldPlaceholder, fieldRequired, optionsArray);

                $('#editFieldModal').modal('hide');
            },
            error: function(xhr) {
                const errorMessage = xhr.responseJSON?.message || 'Error updating field';
                if (typeof show_toastr === 'function') {
                    show_toastr('error', errorMessage);
                } else {
                    alert(errorMessage);
                }
            }
        });
    }

    function updateFieldInPreview(fieldId, fieldName, fieldType, fieldPlaceholder, fieldRequired, optionsArray) {
        console.log('updateFieldInPreview called with:', {fieldId, fieldName, fieldType, fieldPlaceholder, fieldRequired, optionsArray});

        const fieldContainer = $(`.preview-field-container[data-field-id="${fieldId}"]`);

        if (fieldContainer.length === 0) {
            console.log('Field container not found, reloading page');
            location.reload();
            return;
        }

        // Update field type data attribute
        fieldContainer.attr('data-field-type', fieldType);

        // Update field label
        const labelElement = fieldContainer.find('.preview-label');
        const requiredStar = fieldRequired ? ' <span class="text-danger">*</span>' : '';
        labelElement.html(fieldName + requiredStar);

        // Update field input based on type
        const fieldInputContainer = fieldContainer.find('.field-input');
        let newInputHtml = '';

        // Ensure placeholder is never null or undefined
        const safePlaceholder = fieldPlaceholder && fieldPlaceholder.trim() !== '' ? fieldPlaceholder : '';
        const defaultPlaceholder = safePlaceholder || 'Enter ' + fieldName.toLowerCase();
        const selectPlaceholder = safePlaceholder || 'Select ' + fieldName.toLowerCase();

        switch(fieldType) {
            case 'text':
            case 'email':
            case 'number':
                newInputHtml = `<input type="${fieldType}" class="preview-input form-control" placeholder="${defaultPlaceholder}">`;
                break;
            case 'textarea':
                newInputHtml = `<textarea class="preview-input form-control" rows="3" placeholder="${defaultPlaceholder}"></textarea>`;
                break;
            case 'select':
                newInputHtml = `
                    <select class="preview-input form-control">
                        <option>${selectPlaceholder}</option>
                        ${optionsArray.map(option => `<option>${option}</option>`).join('')}
                    </select>
                `;
                break;
            case 'multiselect':
                newInputHtml = `
                    <select class="preview-input form-control" multiple>
                        ${optionsArray.map(option => `<option>${option}</option>`).join('')}
                    </select>
                `;
                break;
            case 'radio':
                newInputHtml = optionsArray.map(option => `
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="radio_${fieldId}" value="${option}">
                        <label class="form-check-label">${option}</label>
                    </div>
                `).join('');
                break;
            case 'checkbox':
                newInputHtml = optionsArray.map(option => `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${option}">
                        <label class="form-check-label">${option}</label>
                    </div>
                `).join('');
                break;
            case 'date':
                newInputHtml = `<input type="date" class="preview-input form-control">`;
                break;
            case 'file':
                newInputHtml = `<input type="file" class="preview-input form-control">`;
                break;
            default:
                newInputHtml = `<input type="text" class="preview-input form-control" placeholder="${defaultPlaceholder}">`;
        }

        // Replace the field input content
        fieldInputContainer.html(newInputHtml);

        console.log(`Field ${fieldId} updated in preview:`, {fieldName, fieldType, fieldPlaceholder, fieldRequired});
    }

    // Initialize Select2 for custom field selector
    function initializeCustomFieldSelect2() {
        if (typeof $.fn.select2 === 'undefined') {
            console.warn('Select2 not loaded, falling back to regular select');
            return;
        }

        $('#custom-field-select').select2({
            placeholder: 'Search and select a custom field...',
            allowClear: true,
            width: '100%',
            templateResult: formatCustomFieldOption,
            templateSelection: formatCustomFieldSelection,
            escapeMarkup: function(markup) { return markup; },
            language: {
                noResults: function() {
                    return "No custom fields found";
                }
            }
        });

        // Handle selection change
        $('#custom-field-select').on('select2:select', function(e) {
            $('#add-custom-field-btn').prop('disabled', false);
        });

        // Handle clear selection
        $('#custom-field-select').on('select2:clear', function(e) {
            $('#add-custom-field-btn').prop('disabled', true);
        });
    }

    // Format custom field option in dropdown
    function formatCustomFieldOption(option) {
        if (!option.id) {
            return option.text;
        }

        const $option = $(option.element);
        const fieldType = $option.data('type') || 'text';
        const isRequired = $option.data('required') === 'true';

        console.log('Formatting option:', {fieldType, isRequired, optionText: option.text});

        // Get field type icon
        const typeIcons = {
            'text': '<i class="ti ti-forms text-primary"></i>',
            'email': '<i class="ti ti-mail text-info"></i>',
            'number': '<i class="ti ti-hash text-success"></i>',
            'phone': '<i class="ti ti-phone text-info"></i>',
            'textarea': '<i class="ti ti-notes text-secondary"></i>',
            'select': '<i class="ti ti-list text-warning"></i>',
            'radio': '<i class="ti ti-circle text-primary"></i>',
            'checkbox': '<i class="ti ti-checkbox text-success"></i>',
            'multiselect': '<i class="ti ti-list-check text-info"></i>',
            'date': '<i class="ti ti-calendar text-danger"></i>',
            'file': '<i class="ti ti-paperclip text-dark"></i>',
            'business': '<i class="ti ti-building text-warning"></i>',
            'custom': '<i class="ti ti-settings text-purple"></i>'
        };

        const icon = typeIcons[fieldType] || '<i class="ti ti-forms text-primary"></i>';
        const requiredBadge = isRequired ? '<span class="required-badge">*</span>' : '';

        const html = `
            <div class="custom-field-option">
                <div class="field-info">
                    <span class="field-icon">${icon}</span>
                    <span class="field-name">${option.text}</span>
                    ${requiredBadge}
                </div>
                <div class="field-type">${fieldType.toUpperCase()}</div>
            </div>
        `;

        console.log('Generated HTML:', html);
        return $(html);
    }

    // Format selected option
    function formatCustomFieldSelection(option) {
        if (!option.id) {
            return option.text;
        }

        const $option = $(option.element);
        const fieldType = $option.data('type') || 'text';

        console.log('Formatting selection:', {fieldType, optionText: option.text});

        const typeIcons = {
            'text': '<i class="ti ti-forms text-primary"></i>',
            'email': '<i class="ti ti-mail text-info"></i>',
            'number': '<i class="ti ti-hash text-success"></i>',
            'phone': '<i class="ti ti-phone text-info"></i>',
            'textarea': '<i class="ti ti-notes text-secondary"></i>',
            'select': '<i class="ti ti-list text-warning"></i>',
            'radio': '<i class="ti ti-circle text-primary"></i>',
            'checkbox': '<i class="ti ti-checkbox text-success"></i>',
            'multiselect': '<i class="ti ti-list-check text-info"></i>',
            'date': '<i class="ti ti-calendar text-danger"></i>',
            'file': '<i class="ti ti-paperclip text-dark"></i>',
            'business': '<i class="ti ti-building text-warning"></i>',
            'custom': '<i class="ti ti-settings text-purple"></i>'
        };

        const icon = typeIcons[fieldType] || '<i class="ti ti-forms text-primary"></i>';
        const html = `<span class="selected-field-icon">${icon}</span> <span class="selected-field-text">${option.text}</span>`;

        console.log('Selection HTML:', html);
        return $(html);
    }



    // Initialize Select2 for field type selector
    function initializeFieldTypeSelect2() {
        if (typeof $.fn.select2 === 'undefined') {
            console.warn('Select2 not loaded for field type selector');
            return;
        }

        $('#new-field-type').select2({
            placeholder: 'Select field type...',
            width: '100%',
            templateResult: formatFieldTypeOption,
            templateSelection: formatFieldTypeSelection,
            escapeMarkup: function(markup) { return markup; },
            minimumResultsForSearch: Infinity, // Disable search for field types
            dropdownParent: $('.create-field-subsection')
        });
    }

    // Format field type option in dropdown
    function formatFieldTypeOption(option) {
        if (!option.id) {
            return option.text;
        }

        const fieldType = option.id;
        console.log('Formatting field type option:', fieldType);

        const typeIcons = {
            'text': '<i class="ti ti-forms text-primary"></i>',
            'email': '<i class="ti ti-mail text-info"></i>',
            'number': '<i class="ti ti-hash text-success"></i>',
            'phone': '<i class="ti ti-phone text-info"></i>',
            'textarea': '<i class="ti ti-notes text-secondary"></i>',
            'select': '<i class="ti ti-list text-warning"></i>',
            'radio': '<i class="ti ti-circle text-primary"></i>',
            'checkbox': '<i class="ti ti-checkbox text-success"></i>',
            'multiselect': '<i class="ti ti-list-check text-info"></i>',
            'date': '<i class="ti ti-calendar text-danger"></i>',
            'file': '<i class="ti ti-paperclip text-dark"></i>',
            'business': '<i class="ti ti-building text-warning"></i>',
            'custom': '<i class="ti ti-settings text-purple"></i>'
        };

        const icon = typeIcons[fieldType] || '<i class="ti ti-forms text-primary"></i>';

        const html = `
            <div class="field-type-option">
                <span class="field-type-icon">${icon}</span>
                <span class="field-type-name">${option.text}</span>
            </div>
        `;

        console.log('Field type HTML:', html);
        return $(html);
    }

    // Format selected field type
    function formatFieldTypeSelection(option) {
        if (!option.id) {
            return option.text;
        }

        const fieldType = option.id;
        const typeIcons = {
            'text': '<i class="ti ti-forms text-primary"></i>',
            'email': '<i class="ti ti-mail text-info"></i>',
            'number': '<i class="ti ti-hash text-success"></i>',
            'phone': '<i class="ti ti-phone text-info"></i>',
            'textarea': '<i class="ti ti-notes text-secondary"></i>',
            'select': '<i class="ti ti-list text-warning"></i>',
            'radio': '<i class="ti ti-circle text-primary"></i>',
            'checkbox': '<i class="ti ti-checkbox text-success"></i>',
            'multiselect': '<i class="ti ti-list-check text-info"></i>',
            'date': '<i class="ti ti-calendar text-danger"></i>',
            'file': '<i class="ti ti-paperclip text-dark"></i>',
            'business': '<i class="ti ti-building text-warning"></i>',
            'custom': '<i class="ti ti-settings text-purple"></i>'
        };

        const icon = typeIcons[fieldType] || '<i class="ti ti-forms text-primary"></i>';
        return `${icon} ${option.text}`;
    }

    // Update custom fields dropdown with new fields
    function updateCustomFieldsDropdown(customFields) {
        const $select = $('#custom-field-select');

        // Clear existing options except the placeholder
        $select.find('option[value!=""]').remove();

        // Add new options
        if (customFields && customFields.length > 0) {
            customFields.forEach(function(field) {
                const option = new Option(field.name, field.id);
                $(option).attr('data-type', field.type);
                $(option).attr('data-required', field.required ? 'true' : 'false');
                $(option).attr('data-placeholder', field.placeholder || '');
                $select.append(option);
            });

            // Trigger change to update Select2
            $select.trigger('change');

            console.log(`Updated custom fields dropdown with ${customFields.length} fields`);

            // Show success message about the new field being available
            show_toastr('info', 'Field added to custom fields library for future use!');
        } else {
            // Add "no custom fields" option if none exist
            const noFieldsOption = new Option('No custom fields available', '');
            $(noFieldsOption).prop('disabled', true);
            $select.append(noFieldsOption);
            $select.trigger('change');

            console.log('No custom fields available, added disabled option');
        }
    }

    // Initial preview update
    updatePreview();
});
</script>

<!-- Select2 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js"></script>

<script>
// Country code dropdown functionality for live preview
$(document).ready(function() {
    let searchTimeout;
    let searchString = '';

    // Handle country dropdown selection in preview
    $(document).on('click', '.country-dropdown .dropdown-item', function(e) {
        e.preventDefault();

        const country = $(this).attr('data-country');
        const code = $(this).attr('data-code');
        const flag = $(this).attr('data-flag');

        // Find the parent container
        const container = $(this).closest('.phone-input-container');
        const button = container.find('.country-dropdown-btn');

        // Update button display
        button.find('.flag').text(flag);
        button.find('.code').text(code);

        // Close dropdown
        button.dropdown('hide');
    });

    // Keyboard search functionality
    $(document).on('keydown', '.country-dropdown', function(e) {
        const dropdown = $(this);
        const items = dropdown.find('.dropdown-item');

        // Clear previous timeout
        clearTimeout(searchTimeout);

        // Add character to search string
        if (e.key.length === 1 && e.key.match(/[a-zA-Z]/)) {
            searchString += e.key.toLowerCase();

            // Find matching item
            let foundItem = null;
            items.each(function() {
                const text = $(this).text().toLowerCase();
                if (text.includes(searchString)) {
                    foundItem = $(this);
                    return false; // Break loop
                }
            });

            // Highlight found item
            if (foundItem) {
                items.removeClass('active');
                foundItem.addClass('active');
                foundItem[0].scrollIntoView({ block: 'nearest' });
            }

            // Clear search string after 1 second
            searchTimeout = setTimeout(() => {
                searchString = '';
            }, 1000);
        }

        // Handle Enter key
        if (e.key === 'Enter') {
            e.preventDefault();
            const activeItem = dropdown.find('.dropdown-item.active');
            if (activeItem.length) {
                activeItem.click();
            }
        }

        // Handle Arrow keys
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            e.preventDefault();
            const activeItem = dropdown.find('.dropdown-item.active');
            let nextItem;

            if (e.key === 'ArrowDown') {
                nextItem = activeItem.length ? activeItem.next('.dropdown-item') : items.first();
            } else {
                nextItem = activeItem.length ? activeItem.prev('.dropdown-item') : items.last();
            }

            if (nextItem.length) {
                items.removeClass('active');
                nextItem.addClass('active');
                nextItem[0].scrollIntoView({ block: 'nearest' });
            }
        }
    });

    // Focus dropdown when opened
    $(document).on('shown.bs.dropdown', '.country-selector', function() {
        $(this).find('.country-dropdown').focus();
    });
});
</script>
@endpush

@section('content')
    @if(isset($form) && $form)
    <div class="row">
        <!-- Style Controls Sidebar -->
        <div class="col-md-5">
            <!-- Field Management Section -->
            <div class="style-controls field-management-section">
                <div class="main-section-header">
                    <h6 class="main-section-title">
                        <i class="ti ti-forms me-2"></i>{{__('Field Management')}}
                    </h6>
                    <small class="text-muted">{{__('Add existing fields or create new ones for your form')}}</small>
                </div>

                <!-- Add Existing Field Subsection -->
                <div class="subsection add-existing-field-subsection">
                    <div class="subsection-header">
                        <h6 class="subsection-title">
                            <i class="ti ti-database me-2"></i>{{__('Add Existing Field')}}
                        </h6>
                        <small class="text-muted">{{__('Add pre-created custom fields')}}</small>
                    </div>

                    <!-- Enhanced Custom Field Selector -->
                    <div class="custom-field-selector">
                        <div class="selector-container">
                            <div class="select-wrapper">
                                <label class="select-label">{{__('Choose Custom Field')}}</label>
                                <select class="custom-field-select2" id="custom-field-select" style="width: 100%;">
                                    <option value="">{{__('Search and select a custom field...')}}</option>
                                    @if(isset($customFields) && $customFields->count() > 0)
                                        @foreach($customFields as $field)
                                            <option value="{{ $field->id }}"
                                                    data-type="{{ $field->type }}"
                                                    data-required="{{ $field->required ? 'true' : 'false' }}"
                                                    data-placeholder="{{ $field->placeholder ?? '' }}">
                                                {{ $field->name }}
                                            </option>
                                        @endforeach
                                    @else
                                        <option disabled>{{__('No custom fields available')}}</option>
                                    @endif
                                </select>
                            </div>
                            <div class="add-button-wrapper">
                                <button type="button" class="add-custom-field-btn" id="add-custom-field-btn" disabled>
                                    <i class="ti ti-plus me-2"></i>
                                    <span class="btn-text">{{__('Add Field')}}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create New Field Subsection -->
                <div class="subsection create-field-subsection">
                    <div class="subsection-header">
                        <h6 class="subsection-title">
                            <i class="ti ti-plus me-2"></i>{{__('Create New Field')}}
                        </h6>
                        <small class="text-muted">{{__('Add a new field to your form')}}</small>
                    </div>

                    <div class="section-content">
                        <form id="create-field-form">
                            <!-- Field Name and Type -->
                            <div class="row g-3 mb-3">
                                <div class="col-12">
                                    <div class="control-group">
                                        <label class="control-label">{{__('Field Name')}} <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="new-field-name" placeholder="{{__('Enter field name')}}" required>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="control-group">
                                        <label class="control-label">{{__('Field Type')}} <span class="text-danger">*</span></label>
                                        <select class="form-select custom-select" id="new-field-type" required>
                                            <option value="text">{{__('Text Input')}}</option>
                                            <option value="email">{{__('Email')}}</option>
                                            <option value="number">{{__('Number')}}</option>
                                            <option value="textarea">{{__('Textarea')}}</option>
                                            <option value="select">{{__('Select Dropdown')}}</option>
                                            <option value="radio">{{__('Radio Buttons')}}</option>
                                            <option value="checkbox">{{__('Checkboxes')}}</option>
                                            <option value="multiselect">{{__('Multi-Select')}}</option>
                                            <option value="date">{{__('Date Picker')}}</option>
                                            <option value="file">{{__('File Upload')}}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Placeholder Field (for text inputs only) -->
                            <div class="row g-3 mb-3" id="placeholder-section">
                                <div class="col-12">
                                    <div class="control-group">
                                        <label class="control-label">{{__('Placeholder Text')}}</label>
                                        <input type="text" class="form-control" id="new-field-placeholder" placeholder="{{__('Enter placeholder text')}}">
                                        <small class="form-text text-muted">{{__('Hint text shown inside the input field')}}</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Options Section (for choice fields only) -->
                            <div class="row g-3 mb-3" id="options-section" style="display: none;">
                                <div class="col-12">
                                    <div class="control-group">
                                        <label class="control-label">{{__('Options')}} <span class="text-danger">*</span></label>
                                        <div class="options-builder">
                                            <div class="options-list" id="new-field-options-list">
                                                <div class="option-item d-flex mb-2">
                                                    <input type="text" class="form-control me-2" placeholder="{{__('Option 1')}}" value="">
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-new-option" disabled>
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </div>
                                                <div class="option-item d-flex mb-2">
                                                    <input type="text" class="form-control me-2" placeholder="{{__('Option 2')}}" value="">
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-new-option">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-outline-secondary add-new-option">
                                                <i class="ti ti-plus me-1"></i>{{__('Add Option')}}
                                            </button>
                                        </div>
                                        <small class="form-text text-muted">{{__('Add options for users to choose from')}}</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Required Field Toggle -->
                            <div class="row g-3 mb-3">
                                <div class="col-12">
                                    <div class="control-group">
                                        <div class="required-toggle d-flex align-items-center">
                                            <input class="form-check-input" type="checkbox" id="new-field-required">
                                            <label class="form-check-label" for="new-field-required">
                                                <span class="toggle-text">{{__('Required Field')}}</span>
                                                <small class="toggle-desc">{{__('Users must fill this field')}}</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Create Button -->
                            <div class="row g-3">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success w-100 create-field-btn" id="add-new-field-btn">
                                        <i class="ti ti-plus me-2"></i>
                                        <span class="btn-text">{{__('Create Field')}}</span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Style Customization Section -->
            <div class="style-controls">
                <div class="style-header mb-3">
                    <h6 class="mb-0 d-flex align-items-center">
                        <i class="ti ti-palette me-2 text-primary"></i>
                        {{__('Style Customization')}}
                    </h6>
                    <small class="text-muted">{{__('Customize your form appearance')}}</small>
                </div>

                <!-- Background Section -->
                <div class="style-section mb-4">
                    <div class="section-header">
                        <h6 class="section-title">
                            <i class="ti ti-color-swatch me-2"></i>{{__('Background & Colors')}}
                        </h6>
                    </div>
                    <div class="section-content">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Background Color')}}</label>
                                    <div class="color-input-group">
                                        <input type="color" class="color-picker" id="bg-color" value="#ffffff">
                                        <div class="color-preview" id="bg-preview"></div>
                                        <span class="color-value">#ffffff</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Input Background')}}</label>
                                    <div class="color-input-group">
                                        <input type="color" class="color-picker" id="input-bg-color" value="#ffffff">
                                        <div class="color-preview" id="input-bg-preview"></div>
                                        <span class="color-value">#ffffff</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="control-group">
                                    <div class="transparent-toggle d-flex align-items-center">
                                        <input class="form-check-input" type="checkbox" id="bg-transparent">
                                        <label class="form-check-label" for="bg-transparent">
                                            <span class="toggle-text">{{__('Transparent Background')}}</span>
                                            <small class="toggle-desc">{{__('Remove background color')}}</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Button Section -->
                <div class="style-section mb-4">
                    <div class="section-header">
                        <h6 class="section-title">
                            <i class="ti ti-click me-2"></i>{{__('Submit Button')}}
                        </h6>
                    </div>
                    <div class="section-content">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Button Color')}}</label>
                                    <div class="color-input-group">
                                        <input type="color" class="color-picker" id="btn-bg-color" value="#007bff">
                                        <div class="color-preview" id="btn-bg-preview"></div>
                                        <span class="color-value">#007bff</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Text Color')}}</label>
                                    <div class="color-input-group">
                                        <input type="color" class="color-picker" id="btn-color" value="#ffffff">
                                        <div class="color-preview" id="btn-color-preview"></div>
                                        <span class="color-value">#ffffff</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="control-group">
                                    <label class="control-label">{{__('Button Position')}}</label>
                                    <div class="button-position-group">
                                        <input type="radio" class="btn-check" name="btn-position" id="btn-left" value="left">
                                        <label class="btn btn-outline-secondary btn-sm" for="btn-left">
                                            <i class="ti ti-align-left"></i> {{__('Left')}}
                                        </label>

                                        <input type="radio" class="btn-check" name="btn-position" id="btn-center" value="center" checked>
                                        <label class="btn btn-outline-secondary btn-sm" for="btn-center">
                                            <i class="ti ti-align-center"></i> {{__('Center')}}
                                        </label>

                                        <input type="radio" class="btn-check" name="btn-position" id="btn-right" value="right">
                                        <label class="btn btn-outline-secondary btn-sm" for="btn-right">
                                            <i class="ti ti-align-right"></i> {{__('Right')}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Typography Section -->
                <div class="style-section mb-4">
                    <div class="section-header">
                        <h6 class="section-title">
                            <i class="ti ti-typography me-2"></i>{{__('Typography & Text')}}
                        </h6>
                    </div>
                    <div class="section-content">
                        <div class="row mb-3 g-3">
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Label Color')}}</label>
                                    <div class="color-input-group">
                                        <input type="color" class="color-picker" id="label-color" value="#212529">
                                        <div class="color-preview" id="label-preview"></div>
                                        <span class="color-value">#212529</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Input Text Color')}}</label>
                                    <div class="color-input-group">
                                        <input type="color" class="color-picker" id="input-color" value="#495057">
                                        <div class="color-preview" id="input-color-preview"></div>
                                        <span class="color-value">#495057</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Font Family')}}</label>
                                    <select class="form-select custom-select" id="font-family">
                                        <option value="Arial, sans-serif">Arial</option>
                                        <option value="Helvetica, sans-serif">Helvetica</option>
                                        <option value="Georgia, serif">Georgia</option>
                                        <option value="Times New Roman, serif">Times New Roman</option>
                                        <option value="Verdana, sans-serif">Verdana</option>
                                        <option value="Tahoma, sans-serif">Tahoma</option>
                                        <option value="Trebuchet MS, sans-serif">Trebuchet MS</option>
                                        <option value="Impact, sans-serif">Impact</option>
                                        <option value="Comic Sans MS, cursive">Comic Sans MS</option>
                                        <option value="Courier New, monospace">Courier New</option>
                                        <option value="Lucida Console, monospace">Lucida Console</option>
                                        <option value="Palatino, serif">Palatino</option>
                                        <option value="Book Antiqua, serif">Book Antiqua</option>
                                        <option value="Century Gothic, sans-serif">Century Gothic</option>
                                        <option value="Franklin Gothic Medium, sans-serif">Franklin Gothic Medium</option>
                                        <option value="Garamond, serif">Garamond</option>
                                        <option value="Gill Sans, sans-serif">Gill Sans</option>
                                        <option value="Lucida Sans, sans-serif">Lucida Sans</option>
                                        <option value="MS Sans Serif, sans-serif">MS Sans Serif</option>
                                        <option value="MS Serif, serif">MS Serif</option>
                                        <option value="Segoe UI, sans-serif">Segoe UI</option>
                                        <option value="Calibri, sans-serif">Calibri</option>
                                        <option value="Cambria, serif">Cambria</option>
                                        <option value="Candara, sans-serif">Candara</option>
                                        <option value="Consolas, monospace">Consolas</option>
                                        <option value="Constantia, serif">Constantia</option>
                                        <option value="Corbel, sans-serif">Corbel</option>
                                        <option value="Optima, sans-serif">Optima</option>
                                        <option value="Futura, sans-serif">Futura</option>
                                        <option value="Avenir, sans-serif">Avenir</option>
                                        <option value="Proxima Nova, sans-serif">Proxima Nova</option>
                                        <option value="Open Sans, sans-serif">Open Sans</option>
                                        <option value="Roboto, sans-serif">Roboto</option>
                                        <option value="Lato, sans-serif">Lato</option>
                                        <option value="Montserrat, sans-serif">Montserrat</option>
                                        <option value="Source Sans Pro, sans-serif">Source Sans Pro</option>
                                        <option value="Nunito, sans-serif">Nunito</option>
                                        <option value="Poppins, sans-serif">Poppins</option>
                                        <option value="Inter, sans-serif">Inter</option>
                                        <option value="Work Sans, sans-serif">Work Sans</option>
                                        <option value="Fira Sans, sans-serif">Fira Sans</option>
                                        <option value="IBM Plex Sans, sans-serif">IBM Plex Sans</option>
                                        <option value="Merriweather, serif">Merriweather</option>
                                        <option value="Playfair Display, serif">Playfair Display</option>
                                        <option value="Crimson Text, serif">Crimson Text</option>
                                        <option value="Libre Baskerville, serif">Libre Baskerville</option>
                                        <option value="Lora, serif">Lora</option>
                                        <option value="PT Serif, serif">PT Serif</option>
                                        <option value="Source Serif Pro, serif">Source Serif Pro</option>
                                        <option value="Vollkorn, serif">Vollkorn</option>
                                        <option value="Bitter, serif">Bitter</option>
                                        <option value="Droid Serif, serif">Droid Serif</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Font Size')}}</label>
                                    <select class="form-select custom-select" id="font-size">
                                        <option value="12px">12px</option>
                                        <option value="14px" selected>14px</option>
                                        <option value="16px">16px</option>
                                        <option value="18px">18px</option>
                                        <option value="18px">20px</option>
                                        <option value="18px">22px</option>
                                        <option value="18px">24px</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Form Title Position -->
                        <div class="row g-3 mb-3">
                            <div class="col-12">
                                <div class="control-group">
                                    <label class="control-label">{{__('Form Title Position')}}</label>
                                    <div class="button-position-group">
                                        <input type="radio" class="btn-check" name="title-position" id="title-left" value="left">
                                        <label class="btn btn-outline-secondary btn-sm" for="title-left">
                                            <i class="ti ti-align-left"></i> {{__('Left')}}
                                        </label>

                                        <input type="radio" class="btn-check" name="title-position" id="title-center" value="center">
                                        <label class="btn btn-outline-secondary btn-sm" for="title-center">
                                            <i class="ti ti-align-center"></i> {{__('Center')}}
                                        </label>

                                        <input type="radio" class="btn-check" name="title-position" id="title-right" value="right">
                                        <label class="btn btn-outline-secondary btn-sm" for="title-right">
                                            <i class="ti ti-align-right"></i> {{__('Right')}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hide Form Title -->
                        <div class="row g-3 mb-3">
                            <div class="col-12">
                                <div class="control-group">
                                    <div class="transparent-toggle d-flex align-items-center">
                                        <input class="form-check-input" type="checkbox" id="hide-form-title">
                                        <label class="form-check-label" for="hide-form-title">
                                            <span class="toggle-text">{{__('Hide Form Title')}}</span>
                                            <small class="toggle-desc">{{__('Hide the form title from display')}}</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Layout Section -->
                <div class="style-section mb-4">
                    <div class="section-header">
                        <h6 class="section-title">
                            <i class="ti ti-layout me-2"></i>{{__('Layout & Spacing')}}
                        </h6>
                    </div>
                    <div class="section-content">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Border Color')}}</label>
                                    <div class="color-input-group">
                                        <input type="color" class="color-picker" id="border-color" value="#ced4da">
                                        <div class="color-preview" id="border-preview"></div>
                                        <span class="color-value">#ced4da</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="control-group">
                                    <label class="control-label">{{__('Custom Border Radius')}}</label>
                                    <div class="input-group">
                                        <input type="range" class="form-range" id="custom-border-radius" min="0" max="100" value="4" step="1">
                                        <span class="input-group-text" id="border-radius-value">4px</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="control-group">
                                    <label class="control-label">{{__('Field Spacing')}}</label>
                                    <div class="spacing-options">
                                        <input type="radio" class="btn-check" name="field-spacing" id="spacing-10" value="10px">
                                        <label class="btn btn-outline-secondary btn-sm" for="spacing-10">Compact</label>

                                        <input type="radio" class="btn-check" name="field-spacing" id="spacing-15" value="15px" checked>
                                        <label class="btn btn-outline-secondary btn-sm" for="spacing-15">Normal</label>

                                        <input type="radio" class="btn-check" name="field-spacing" id="spacing-20" value="20px">
                                        <label class="btn btn-outline-secondary btn-sm" for="spacing-20">Relaxed</label>

                                        <input type="radio" class="btn-check" name="field-spacing" id="spacing-25" value="25px">
                                        <label class="btn btn-outline-secondary btn-sm" for="spacing-25">Spacious</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="style-section">
                    <div class="section-header">
                        <h6 class="section-title">
                            <i class="ti ti-settings me-2"></i>{{__('Actions')}}
                        </h6>
                    </div>
                    <div class="section-content">
                        <div class="style-action-buttons">
                            <button type="button" class="style-save-btn" id="save-styles">
                                <div class="btn-content">
                                    <i class="ti ti-device-floppy"></i>
                                    <div class="btn-text-content">
                                        <span class="btn-title">{{__('Save Styles')}}</span>
                                        <small class="btn-subtitle">{{__('Apply changes')}}</small>
                                    </div>
                                </div>
                            </button>
                            <button type="button" class="style-reset-btn" id="reset-styles">
                                <div class="btn-content">
                                    <i class="ti ti-refresh"></i>
                                    <div class="btn-text-content">
                                        <span class="btn-title">{{__('Reset Styles')}}</span>
                                        <small class="btn-subtitle">{{__('Back to defaults')}}</small>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Preview -->
        <div class="col-md-7">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">{{__('Live Preview')}}</h6>
                <small class="text-muted">{{__('Drag fields to reorder • Click to edit')}}</small>
            </div>
            <div class="form-preview">
                <div class="form-preview-container">
                    <div id="form-preview" class="card shadow-sm">
                        <div class="card-body p-2">
                            <h4 class="preview-title mb-4">{{ $form->name }}</h4>

                            <div id="sortable-fields" class="sortable-container">
                                @if($form->form_field && $form->form_field->count() > 0)
                                    @foreach($form->form_field as $field)
                                        <div class="preview-field-container mb-3" data-field-id="{{ $field->id }}" data-field-type="{{ $field->type }}">

                                        <div class="preview-field">
                                            <div class="field-header d-flex align-items-center justify-content-between mb-2">
                                                <label class="preview-label form-label fw-medium mb-0">{{ $field->name }}
                                                    @if($field->required)
                                                        <span class="text-danger">*</span>
                                                    @endif
                                                </label>
                                                <div class="field-actions">
                                                    <button type="button" class="btn btn-sm btn-outline-primary edit-field-btn" data-field-id="{{ $field->id }}" title="Edit Field">
                                                        <i class="ti ti-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger delete-field-btn" data-field-id="{{ $field->id }}" title="Delete Field">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="field-input-container d-flex align-items-center">
                                                <div class="drag-handle me-2">
                                                    <i class="ti ti-grip-vertical text-muted"></i>
                                                </div>
                                                <div class="field-input flex-grow-1">

                                            @if($field->type == 'text' || $field->type == 'email')
                                                <input type="{{ $field->type }}" class="preview-input form-control" placeholder="{{ $field->placeholder ?? 'Enter ' . strtolower($field->name) }}">
                                            @elseif($field->type == 'number')
                                                @php
                                                    $isPhoneField = stripos($field->name, 'contact') !== false ||
                                                                   stripos($field->name, 'phone') !== false ||
                                                                   stripos($field->name, 'mobile') !== false;
                                                @endphp
                                                @if($isPhoneField)
                                                    <div class="phone-input-container">
                                                        <div class="country-selector">
                                                            <button type="button" class="country-dropdown-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <span class="flag">🇮🇳</span>
                                                                <span class="code">+91</span>
                                                                <i class="ti ti-chevron-down"></i>
                                                            </button>
                                                            <ul class="dropdown-menu country-dropdown">
                                                                <li><a class="dropdown-item" href="#" data-country="IN" data-code="+91" data-flag="🇮🇳">🇮🇳 India (+91)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="US" data-code="+1" data-flag="🇺🇸">🇺🇸 United States (+1)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="GB" data-code="+44" data-flag="🇬🇧">🇬🇧 United Kingdom (+44)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="CN" data-code="+86" data-flag="🇨🇳">🇨🇳 China (+86)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="JP" data-code="+81" data-flag="🇯🇵">🇯🇵 Japan (+81)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="DE" data-code="+49" data-flag="🇩🇪">🇩🇪 Germany (+49)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="FR" data-code="+33" data-flag="🇫🇷">🇫🇷 France (+33)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="IT" data-code="+39" data-flag="🇮🇹">🇮🇹 Italy (+39)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="ES" data-code="+34" data-flag="🇪🇸">🇪🇸 Spain (+34)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="RU" data-code="+7" data-flag="🇷🇺">🇷🇺 Russia (+7)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="BR" data-code="+55" data-flag="🇧🇷">🇧🇷 Brazil (+55)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="AU" data-code="+61" data-flag="🇦🇺">🇦🇺 Australia (+61)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="KR" data-code="+82" data-flag="🇰🇷">🇰🇷 South Korea (+82)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="SG" data-code="+65" data-flag="🇸🇬">🇸🇬 Singapore (+65)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="MY" data-code="+60" data-flag="🇲🇾">🇲🇾 Malaysia (+60)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="TH" data-code="+66" data-flag="🇹🇭">🇹🇭 Thailand (+66)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="VN" data-code="+84" data-flag="🇻🇳">🇻🇳 Vietnam (+84)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="ID" data-code="+62" data-flag="🇮🇩">🇮🇩 Indonesia (+62)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="PH" data-code="+63" data-flag="🇵🇭">🇵🇭 Philippines (+63)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="AE" data-code="+971" data-flag="🇦🇪">🇦🇪 UAE (+971)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="SA" data-code="+966" data-flag="🇸🇦">🇸🇦 Saudi Arabia (+966)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="ZA" data-code="+27" data-flag="🇿🇦">🇿🇦 South Africa (+27)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="EG" data-code="+20" data-flag="🇪🇬">🇪🇬 Egypt (+20)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="MX" data-code="+52" data-flag="🇲🇽">🇲🇽 Mexico (+52)</a></li>
                                                                <li><a class="dropdown-item" href="#" data-country="AR" data-code="+54" data-flag="🇦🇷">🇦🇷 Argentina (+54)</a></li>
                                                            </ul>
                                                        </div>
                                                        <input type="number" class="preview-input form-control phone-number-input" placeholder="{{ $field->placeholder ?? 'Enter ' . strtolower($field->name) }}">
                                                    </div>
                                                @else
                                                    <input type="number" class="preview-input form-control" placeholder="{{ $field->placeholder ?? 'Enter ' . strtolower($field->name) }}">
                                                @endif
                                            @elseif($field->type == 'textarea')
                                                <textarea class="preview-input form-control" rows="3" placeholder="{{ $field->placeholder ?? 'Enter ' . strtolower($field->name) }}"></textarea>
                                            @elseif($field->type == 'select')
                                                <select class="preview-input form-control">
                                                    <option>{{ $field->placeholder ?? 'Select ' . strtolower($field->name) }}</option>
                                                    @if($field->options)
                                                        @foreach($field->options as $option)
                                                            <option>{{ $option }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            @elseif($field->type == 'multiselect')
                                                <select class="preview-input form-control" multiple>
                                                    @if($field->options)
                                                        @foreach($field->options as $option)
                                                            <option>{{ $option }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            @elseif($field->type == 'radio')
                                                @if($field->options)
                                                    @foreach($field->options as $option)
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="radio" name="radio_{{ $field->id }}" value="{{ $option }}">
                                                            <label class="form-check-label">{{ $option }}</label>
                                                        </div>
                                                    @endforeach
                                                @endif
                                            @elseif($field->type == 'checkbox')
                                                @if($field->options)
                                                    @foreach($field->options as $option)
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" value="{{ $option }}">
                                                            <label class="form-check-label">{{ $option }}</label>
                                                        </div>
                                                    @endforeach
                                                @endif
                                            @elseif($field->type == 'date')
                                                <input type="date" class="preview-input form-control">
                                            @elseif($field->type == 'file')
                                                <input type="file" class="preview-input form-control">
                                            @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                @else
                                    <div class="text-center py-4">
                                        <p class="text-muted">{{__('No fields added yet. Create your first field using the form above.')}}</p>
                                    </div>
                                @endif
                            </div>

                            <div class="preview-button-container mt-4" id="button-container">
                                <button type="button" class="preview-button btn btn-primary">{{__('Enquiry Now')}}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @else
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger">
                    {{__('Form not found or error loading form data.')}}
                </div>
            </div>
        </div>
    @endif
@endsection
